// https://nuxt.com/docs/api/configuration/nuxt-config

// 获取当前环境
const isDev = process.env.NODE_ENV === 'development';
const isStaging = process.env.NUXT_PUBLIC_ENV === 'staging';
const isProd = process.env.NODE_ENV === 'production' && !isStaging;

export default defineNuxtConfig({
    compatibilityDate: '2025-07-15',

    // 开发工具配置 - 根据环境动态启用
    devtools: {
        enabled: isDev || isStaging,
    },

    // 模块配置
    modules: ['@nuxt/content', '@element-plus/nuxt', '@pinia/nuxt', '@vueuse/nuxt', '@nuxt/image'],

    // CSS配置
    css: ['element-plus/dist/index.css', '~/assets/styles/main.scss'],

    // Element Plus配置
    // elementPlus: {
    //     /** Options */
    // },

    // 环境相关配置
    ssr: process.env.NUXT_SSR !== 'false',

    // 构建配置 - 根据环境优化
    nitro: {
        minify: isProd,
        sourceMap: isDev || isStaging,
    },

    // SEO配置 - 根据环境调整
    app: {
        head: {
            title: process.env.NUXT_PUBLIC_APP_NAME || '瑞物云商城',
            meta: [
                { charset: 'utf-8' },
                { name: 'viewport', content: 'width=device-width, initial-scale=1' },
                { name: 'description', content: process.env.NUXT_PUBLIC_APP_DESCRIPTION || '瑞物云电气设备商城，专业的电气设备采购平台' },
                { name: 'keywords', content: '电气设备,工业设备,瑞物云,商城' },
                // 根据环境设置robots
                { name: 'robots', content: process.env.NUXT_PUBLIC_ROBOTS_ENABLED === 'true' ? 'index,follow' : 'noindex,nofollow' },
            ],
            link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
        },
    },

    // 运行时配置
    runtimeConfig: {
        // 私有配置（仅服务端可用）
        apiSecret: process.env.NUXT_API_SECRET,

        // 公共配置（客户端也可用）
        public: {
            env: process.env.NUXT_PUBLIC_ENV || 'development',
            appName: process.env.NUXT_PUBLIC_APP_NAME || '瑞物云商城',
            appVersion: process.env.NUXT_PUBLIC_APP_VERSION || '1.0.0',
            baseUrl: process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000',
            apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api',
            apiTimeout: parseInt(process.env.NUXT_PUBLIC_API_TIMEOUT || '30000'),
            cdnUrl: process.env.NUXT_PUBLIC_CDN_URL || '',
            debug: process.env.NUXT_PUBLIC_DEBUG === 'true',
            enableAnalytics: process.env.NUXT_PUBLIC_ENABLE_ANALYTICS === 'true',
            enableErrorTracking: process.env.NUXT_PUBLIC_ENABLE_ERROR_TRACKING === 'true',
            googleAnalyticsId: process.env.NUXT_PUBLIC_GOOGLE_ANALYTICS_ID || '',
            sentryDsn: process.env.NUXT_PUBLIC_SENTRY_DSN || '',
        },
    },

    // 图片优化配置
    // image: {
    //     // Options
    // },

    // Vite配置
    vite: {
        resolve: {
            alias: {
                '@': '.',
                '~': '.',
            },
        },
    },
});
