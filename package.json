{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"dev": "nuxt dev --dotenv .env.development", "dev:staging": "nuxt dev --dotenv .env.staging", "build": "nuxt build --dotenv .env.production", "build:dev": "nuxt build --dotenv .env.development", "build:staging": "nuxt build --dotenv .env.staging", "build:prod": "nuxt build --dotenv .env.production", "generate": "nuxt generate --dotenv .env.production", "generate:staging": "nuxt generate --dotenv .env.staging", "preview": "nuxt preview", "preview:staging": "nuxt preview --dotenv .env.staging", "preview:prod": "nuxt preview --dotenv .env.production", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "nuxt typecheck", "clean": "rm -rf .nuxt .output dist node_modules/.cache"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@element-plus/nuxt": "^1.1.4", "@nuxt/content": "^3.6.3", "@nuxt/image": "^1.11.0", "@pinia/nuxt": "^0.11.2", "@vueuse/core": "^13.8.0", "@vueuse/nuxt": "^13.8.0", "better-sqlite3": "^12.2.0", "element-plus": "^2.11.1", "nuxt": "3.17.7", "pinia": "^3.0.3", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"sass": "^1.91.0"}}