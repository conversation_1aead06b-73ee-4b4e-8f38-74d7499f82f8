<template>
    <!-- 使用Element Plus的Affix组件实现固定功能 -->
    <el-affix :offset="0" @change="handleAffixChange">
        <header class="app-header">
            <div class="app-header__top">
                <div class="app-header__container">
                    <!-- 左侧信息 -->
                    <div class="app-header__info">
                        <span class="app-header__info-title">欢迎光临瑞物云商城</span>
                        <span class="app-header__info-subtitle">即刻搜索你想找的服务</span>
                    </div>

                    <!-- 右侧用户区域 -->
                    <div class="app-header__user">
                        <template v-if="!userInfo.isLoggedIn">
                            <NuxtLink to="/" class="app-header__link">瑞物商城首页</NuxtLink>
                            <img src="~/assets/images/home/<USER>" alt="默认头像" />
                            <el-button type="primary" link @click="handleLogin">点击登录</el-button>
                        </template>

                        <template v-else>
                            <span>欢迎您，</span>
                            <el-dropdown @command="handleUserAction">
                                <span class="app-header__username">
                                    {{ userInfo.username }}
                                    <el-icon><ArrowDown /></el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                                        <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                                        <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                    </div>
                </div>
            </div>

            <!-- 主搜索区域 -->
            <div class="app-header__main">
                <div class="app-header__main-container">
                    <!-- Logo区域 -->
                    <NuxtLink to="/" class="app-header__logo">
                        <img src="~/assets/images/home/<USER>" alt="瑞物云" />
                    </NuxtLink>

                    <!-- 搜索框区域 -->
                    <div class="app-header__search">
                        <el-autocomplete v-model="searchKeyword" :fetch-suggestions="querySearchAsync" :placeholder="searchPlaceholder" size="large" @select="handleSelect" @keyup.enter="handleSearch" clearable>
                            <template #prepend>
                                <el-select v-model="searchType" placeholder="方案" style="width: 95px">
                                    <el-option v-for="type in searchTypes" :key="type.value" :label="type.label" :value="type.value" />
                                </el-select>
                            </template>
                            <template #append>
                                <el-button type="primary" @click="handleSearch"> 搜索 </el-button>
                            </template>
                        </el-autocomplete>
                    </div>

                    <!-- 会员推广 -->
                    <div class="app-header__member" @click="handleMemberPromo">
                        <img src="~/assets/images/home/<USER>" alt="免费领取会员" />
                        <span>免费领取会员</span>
                    </div>
                </div>

                <!-- 热搜导航 -->
                <div class="app-header__main-container">
                    <div class="app-header__hotwords">
                        <span class="app-header__hotwords-title">热搜词：</span>
                        <span class="app-header__hotwords-item" v-for="nav in topNavigation" :key="nav.id" @click="handleNavClick(nav)">
                            {{ nav.name }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 页面导航区域 -->
            <div class="app-header__page-nav">
                <div class="app-header__page-container">
                    <!-- 左侧分类导航（悬浮模式） - 固定模式下隐藏 -->
                    <div class="app-header__category-nav">
                        <HomeCategorySidebar ref="categoryNavRef" @category-click="handleCategoryClick" @sidebar-toggle="handleSidebarToggle" />
                    </div>

                    <!-- 页面导航 -->
                    <div class="app-header__main-nav">
                        <HomeNavigation @nav-click="handlePageNavClick" @dropdown-click="handlePageDropdownClick" />
                    </div>
                </div>
            </div>
        </header>
    </el-affix>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue';

// 响应式数据
const searchType = ref('solution');
const searchKeyword = ref('');

// 用户信息状态
const userInfo = reactive({
    isLoggedIn: false, // 可以通过props或store控制
    username: '张三',
    avatar: '',
});

// 搜索类型
const searchTypes = reactive([
    { value: 'solution', label: '方案' },
    { value: 'product', label: '产品' },
]);

// 顶部导航
const topNavigation = reactive([
    { id: 1, name: '数据中心', url: '/category/datacenter', code: 'datacenter' },
    { id: 2, name: '安防监控', url: '/category/security', code: 'security' },
    { id: 3, name: '交通运输', url: '/category/transport', code: 'transport' },
    { id: 4, name: '应急通信', url: '/category/emergency', code: 'emergency' },
    { id: 5, name: '石油石化', url: '/category/petrochemical', code: 'petrochemical' },
    { id: 6, name: '安防监控', url: '/category/security2', code: 'security2' },
    { id: 7, name: '交通运输', url: '/category/transport2', code: 'transport2' },
    { id: 8, name: '安全用电', url: '/category/transport2', code: 'transport2' },
]);

// 计算属性
const searchPlaceholder = computed(() => {
    return searchType.value === 'product' ? '请输入产品' : '请输入方案';
});

// 事件处理
const emit = defineEmits(['search', 'inquiry', 'nav-click', 'login']);

const handleSearch = () => {
    if (!searchKeyword.value.trim()) {
        ElMessage.warning('请输入搜索关键字');
        return;
    }

    emit('search', {
        type: searchType.value,
        keyword: searchKeyword.value.trim(),
    });
};

const handleNavClick = (nav) => {
    emit('nav-click', nav);
};

const handleLogin = () => {
    emit('login');
};

// 页面导航事件处理
const handlePageNavClick = (nav) => {
    console.log('页面导航点击:', nav);
    // 可以在这里添加页面导航的处理逻辑
};

const handlePageDropdownClick = (type, item) => {
    console.log('页面导航下拉点击:', type, item);
    // 可以在这里添加下拉菜单的处理逻辑
};

// 注入分类导航控制
const categoryNavControl = inject('categoryNavControl', null);

// 分类导航事件处理
const handleCategoryClick = (category) => {
    console.log('分类点击:', category);
    // 可以在这里添加分类点击的处理逻辑
};

const handleSidebarToggle = (isExpanded) => {
    console.log('分类导航切换:', isExpanded);
    // 通知页面分类导航状态变化
    if (categoryNavControl) {
        categoryNavControl.setCategoryExpanded(isExpanded);
    }
};

// 用户操作处理
const handleUserAction = (command) => {
    switch (command) {
        case 'profile':
            emit('profile');
            break;
        case 'orders':
            navigateTo('/orders');
            break;
        case 'logout':
            userInfo.isLoggedIn = false;
            emit('logout');
            break;
    }
};

// 会员推广
const handleMemberPromo = () => {
    navigateTo('/member');
};
const categoryNavRef = ref(null);
// Affix组件的change事件处理
const handleAffixChange = (fixed) => {
    console.log('固定状态变化:', categoryNavRef.value, fixed);
    categoryNavRef.value?.handleFixed(!fixed);
};

// 自动完成搜索建议
const searchSuggestions = reactive([
    { value: '数据中心解决方案', category: '方案' },
    { value: '安防监控系统', category: '方案' },
    { value: '交通运输管理', category: '方案' },
    { value: '应急通信设备', category: '产品' },
    { value: '石油石化设备', category: '产品' },
    { value: '网络安全产品', category: '产品' },
]);

const querySearchAsync = (queryString, cb) => {
    const results = queryString ? searchSuggestions.filter((item) => item.value.toLowerCase().includes(queryString.toLowerCase())) : searchSuggestions;

    // 模拟异步查询
    setTimeout(() => {
        cb(results);
    }, 100);
};

const handleSelect = (item) => {
    searchKeyword.value = item.value;
    handleSearch();
};
</script>

<style lang="scss" scoped>
// 应用头部
.app-header {
    background-color: #f5f5f5;
    position: relative;
    z-index: 100;

    // 容器
    &__container {
        max-width: 1920px;
        margin: 0 auto;
        padding: 0 40px;
        display: flex;
        align-items: center;
        gap: 16px;
    }

    // 顶部信息栏
    &__top {
        height: 68px;
        line-height: 68px;
        margin-bottom: 16px;
        font-size: 16px;
        color: #6b7280;
        background-color: #fff;
    }

    &__info {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #1d2129;

        &-title {
            font-size: 20px;
            font-weight: 700;
        }

        &-subtitle {
            font-size: 16px;
        }
    }

    &__user {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: auto;
    }

    &__link {
        margin-right: 24px;
        color: #1d2129;
        text-decoration: none;

        &:hover {
            color: #3b82f6;
        }
    }

    &__username {
        color: #3b82f6;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 24px;

        &:hover {
            color: #2563eb;
        }
    }

    // 主搜索区域
    &__main {
        padding: 40px 0 20px;
        background-color: #fff;

        &-container {
            display: flex;
            align-items: center;
            width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
        }
    }

    &__logo {
        margin-right: 20px;
        img {
            height: 50px;
            width: auto;
        }
    }

    &__search {
        flex: 1;
        max-width: 700px;
    }

    &__member {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        height: 44px;
        margin-left: 18px;
        padding: 0 24px;
        border-radius: 12px;
        background: linear-gradient(133deg, #eec497 0%, #f9e0c3 50%, #f1dbb9 100%);
        cursor: pointer;

        span {
            background: linear-gradient(225deg, #87581b 0%, #5b3410 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-fill-color: transparent;
            font-size: 16px;
            font-weight: 700;
        }
    }

    // 热搜词
    &__hotwords {
        margin-left: 198px;
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 4px 0;

        &-title {
            color: #1d2129;
            font-size: 14px;
            white-space: nowrap;
        }

        &-item {
            color: #4e5969;
            font-size: 14px;
            cursor: pointer;

            &:hover {
                color: #3b82f6;
            }
        }
    }

    // 页面导航区域
    &__page-nav {
        position: relative;
        padding-bottom: 16px;
    }

    &__page-container {
        width: 1200px;
        margin: 16px auto 0;
    }

    // 分类导航容器
    &__category-nav {
        position: absolute;
        top: 0;
        z-index: 1001;
    }

    // 主导航容器
    &__main-nav {
        width: 100%;
        padding-left: 272px; // 为分类导航留出空间（256px + 16px间距）
        box-sizing: border-box;

        // 固定模式下占据全宽
        &--full-width {
            padding-left: 0;
        }
    }
}

// 搜索框特定样式
.app-header__search {
    // Element Plus 组件样式覆盖
    :deep(.el-autocomplete) {
        border-radius: 12px;
        border: 2px solid transparent;
        background:
            linear-gradient(#fff, #fff) padding-box,
            /* 内容区域背景（与父容器背景一致） */ linear-gradient(316deg, rgba(5.31, 120.17, 255, 1), rgba(21.25, 212.93, 255, 1)) border-box;

        box-sizing: border-box;
    }

    :deep(.el-input__inner::placeholder) {
        font-size: 16px;
    }

    :deep(.el-input-group__prepend),
    :deep(.el-input-group__append) {
        background-color: transparent;
        border: none;
        box-shadow: none;
    }

    :deep(.el-button) {
        font-size: 16px;
        background-image: linear-gradient(225.64deg, #0578ff 0%, #15d5ff 100%);

        /* 2. 将背景裁剪到文字区域（核心属性） */
        -webkit-background-clip: text; /* Safari/Chrome 兼容前缀 */
        background-clip: text;

        /* 3. 文字填充色设为透明（露出背景渐变） */
        -webkit-text-fill-color: transparent; /* Safari/Chrome 兼容前缀 */
        text-fill-color: transparent;
    }

    :deep(.el-input__wrapper) {
        padding-left: 0;
        box-shadow: none;

        &:hover {
            box-shadow: none;
        }
    }

    :deep(.el-select__wrapper) {
        padding-left: 20px;
        box-shadow: none;
        font-size: 16px;
    }

    :deep(.el-input-group--prepend .el-input-group__prepend .el-select .el-select__wrapper) {
        border: none;
        box-shadow: none;
    }

    :deep(.el-select__caret) {
        font-size: 18px;
        color: #86909c;
        font-weight: 700;
    }
}
</style>
