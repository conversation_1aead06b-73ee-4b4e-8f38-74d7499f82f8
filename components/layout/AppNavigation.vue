<template>
    <div class="main-nav bg-blue-600 text-white shadow-lg">
        <div class="max-w-[1200px] mx-auto px-4">
            <div class="flex items-center">
                <!-- 主导航菜单 -->
                <nav class="flex items-center space-x-8">
                    <a v-for="item in mainNavItems" :key="item.id" :href="item.url" class="flex items-center py-3 hover:text-blue-200 transition-colors group" :class="{ 'text-blue-200': item.active }" @click.prevent="handleNavClick(item)">
                        <i :class="item.icon" class="mr-2 group-hover:scale-110 transition-transform"></i>
                        <span>{{ item.name }}</span>
                        <span v-if="item.badge" class="ml-2 bg-red-500 text-xs px-2 py-1 rounded-full">
                            {{ item.badge }}
                        </span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- 分类下拉菜单 -->
        <transition name="slide-down">
            <div v-if="showCategoryMenu" class="absolute top-full left-0 w-full bg-white shadow-lg z-50">
                <div class="max-w-[1200px] mx-auto px-4">
                    <div class="grid grid-cols-6 gap-6 py-6">
                        <div v-for="category in categoryMenuItems" :key="category.id" class="space-y-2">
                            <h4 class="font-bold text-gray-800 text-sm">{{ category.name }}</h4>
                            <ul class="space-y-1">
                                <li v-for="subItem in category.children" :key="subItem.id">
                                    <a :href="subItem.url" class="text-gray-600 hover:text-blue-600 text-sm block py-1 transition-colors" @click.prevent="handleSubNavClick(subItem)">
                                        {{ subItem.name }}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script setup>
// 响应式数据
const showCategoryMenu = ref(false);

// 分类按钮
const categoryButton = reactive({
    text: '瑞物云在线商城',
    icon: 'fas fa-bars',
});

// 主导航项
const mainNavItems = reactive([
    {
        id: 1,
        name: '首页',
        url: '/',
        icon: 'fas fa-home',
        active: true,
        code: 'home',
    },
    {
        id: 2,
        name: '成套方案',
        url: '/solutions',
        icon: 'fas fa-cogs',
        code: 'solutions',
    },
    {
        id: 3,
        name: '数据中心',
        url: '/datacenter',
        icon: 'fas fa-server',
        code: 'datacenter',
        badge: 'HOT',
    },
    {
        id: 4,
        name: '合作商务',
        url: '/business',
        icon: 'fas fa-handshake',
        code: 'business',
    },
    {
        id: 5,
        name: '销售网点',
        url: '/stores',
        icon: 'fas fa-map-marker-alt',
        code: 'stores',
    },
    {
        id: 6,
        name: '入驻商户',
        url: '/merchants',
        icon: 'fas fa-store',
        code: 'merchants',
    },
    {
        id: 7,
        name: '关于我们',
        url: '/about',
        icon: 'fas fa-info-circle',
        code: 'about',
    },
]);

// 分类菜单项
const categoryMenuItems = reactive([
    {
        id: 1,
        name: '电源设备',
        children: [
            { id: 11, name: 'UPS电源', url: '/category/ups' },
            { id: 12, name: 'EPS电源', url: '/category/eps' },
            { id: 13, name: '蓄电池', url: '/category/battery' },
            { id: 14, name: '充电桩', url: '/category/charger' },
        ],
    },
    {
        id: 2,
        name: '配电设备',
        children: [
            { id: 21, name: '配电柜', url: '/category/distribution-cabinet' },
            { id: 22, name: '开关柜', url: '/category/switch-cabinet' },
            { id: 23, name: '变压器', url: '/category/transformer' },
            { id: 24, name: '电缆桥架', url: '/category/cable-tray' },
        ],
    },
    {
        id: 3,
        name: '监控设备',
        children: [
            { id: 31, name: '环境监控', url: '/category/environment' },
            { id: 32, name: '视频监控', url: '/category/video' },
            { id: 33, name: '门禁系统', url: '/category/access-control' },
            { id: 34, name: '报警系统', url: '/category/alarm' },
        ],
    },
    {
        id: 4,
        name: '制冷设备',
        children: [
            { id: 41, name: '精密空调', url: '/category/precision-ac' },
            { id: 42, name: '新风系统', url: '/category/fresh-air' },
            { id: 43, name: '冷却塔', url: '/category/cooling-tower' },
            { id: 44, name: '热交换器', url: '/category/heat-exchanger' },
        ],
    },
    {
        id: 5,
        name: '机房设备',
        children: [
            { id: 51, name: '服务器机柜', url: '/category/server-cabinet' },
            { id: 52, name: '网络机柜', url: '/category/network-cabinet' },
            { id: 53, name: '防静电地板', url: '/category/raised-floor' },
            { id: 54, name: '机房装修', url: '/category/room-decoration' },
        ],
    },
    {
        id: 6,
        name: '智能化系统',
        children: [
            { id: 61, name: '楼宇自控', url: '/category/building-automation' },
            { id: 62, name: '能耗管理', url: '/category/energy-management' },
            { id: 63, name: '运维平台', url: '/category/operation-platform' },
            { id: 64, name: '云平台', url: '/category/cloud-platform' },
        ],
    },
]);

// 事件处理
const emit = defineEmits(['nav-click', 'sub-nav-click', 'category-toggle']);

const toggleCategoryMenu = () => {
    showCategoryMenu.value = !showCategoryMenu.value;
    emit('category-toggle', showCategoryMenu.value);
};

const handleNavClick = (item) => {
    // 更新活跃状态
    mainNavItems.forEach((nav) => (nav.active = false));
    item.active = true;

    emit('nav-click', item);
};

const handleSubNavClick = (subItem) => {
    showCategoryMenu.value = false;
    emit('sub-nav-click', subItem);
};

// 点击外部关闭菜单
onMounted(() => {
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.main-nav')) {
            showCategoryMenu.value = false;
        }
    });
});
</script>

<style scoped>
.main-nav {
    position: relative;
}

.slide-down-enter-active,
.slide-down-leave-active {
    transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.rotate-180 {
    transform: rotate(180deg);
}
</style>
