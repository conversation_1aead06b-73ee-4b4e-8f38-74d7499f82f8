<template>
    <footer class="app-footer">
        <div class="footer-container">
            <!-- 主要内容区域 -->
            <div class="footer-content">
                <!-- 左侧公司信息 -->
                <div class="footer-company">
                    <div class="company-logo">
                        <img src="/assets/images/home/<USER>" alt="瑞物云" class="logo-image" />
                    </div>
                    <div class="company-text">
                        <h6>关于瑞物云</h6>
                        <div class="company-divider"></div>
                        <p class="company-description">瑞物云，方案设计和报价清单共享平台。平台以方案为载体，连接了厂家、代理商、集成商、直接用户，为用户提供互动机制，满足用户的采购、生产、定制需求。</p>
                        <div class="company-more">
                            了解更多
                            <el-icon>
                                <ArrowRight />
                            </el-icon>
                        </div>
                    </div>
                </div>

                <!-- 中间链接区域 -->
                <div class="footer-links">
                    <!-- 方案库 -->
                    <div class="link-group">
                        <h3 class="group-title">方案库</h3>
                        <div class="group-divider"></div>
                        <div class="link-columns">
                            <ul class="link-list">
                                <li><NuxtLink to="/solutions/power-system">配电系统</NuxtLink></li>
                                <li><NuxtLink to="/solutions/distribution">配电柜</NuxtLink></li>
                                <li><NuxtLink to="/solutions/ups">UPS系统</NuxtLink></li>
                                <li><NuxtLink to="/solutions/hvac">空调系统</NuxtLink></li>
                            </ul>
                            <ul class="link-list">
                                <li><NuxtLink to="/solutions/eps">EPS系统</NuxtLink></li>
                                <li><NuxtLink to="/solutions/lightning">防雷接地系统</NuxtLink></li>
                                <li><NuxtLink to="/solutions/monitoring">监控系统</NuxtLink></li>
                                <li><NuxtLink to="/solutions/fire">消防系统</NuxtLink></li>
                            </ul>
                        </div>
                    </div>

                    <!-- 联系我们 -->
                    <div class="link-group">
                        <h3 class="group-title">联系我们</h3>
                        <div class="group-divider group-divider--contact"></div>
                        <div class="contact-info">
                            <div class="contact-item">
                                <el-icon class="contact-icon">
                                    <Phone />
                                </el-icon>
                                <span>13910362708</span>
                            </div>
                            <div class="contact-item">
                                <el-icon class="contact-icon">
                                    <Message />
                                </el-icon>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <el-icon class="contact-icon">
                                    <Location />
                                </el-icon>
                                <span>常州市武进区湖塘镇湖塘科技产业园工业坊标准厂房D2栋</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧二维码区域 -->
                <div class="footer-qrcodes">
                    <div class="qrcode-item">
                        <span class="qrcode-label">微信公众号</span>
                        <img src="/assets/images/home/<USER>" alt="微信公众号" class="qrcode-image" />
                    </div>
                    <div class="qrcode-item">
                        <span class="qrcode-label">客服微信</span>
                        <img src="/assets/images/home/<USER>" alt="客服微信" class="qrcode-image" />
                    </div>
                </div>
            </div>

            <!-- 底部版权信息 -->
            <div class="footer-bottom">
                <p class="copyright">版权所有©常州瑞物科技有限公司苏ICP备2023052413号Copyright@2020-{{ new Date().getFullYear() }} All rightsreserved</p>
            </div>
        </div>
    </footer>
</template>

<script setup lang="ts">
import { ArrowRight, Phone, Message, Location } from '@element-plus/icons-vue';
// 页脚组件
</script>

<style lang="scss" scoped>
.app-footer {
    background-color: #2d2d2d;
    padding: 61px 0 33px;
    color: #ffffff;
    background-image: url('/assets/images/home/<USER>');
    background-repeat: no-repeat;
    background-position: center top;
}

.footer-container {
    width: 1500px;
    margin: 0 auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 74px;
}

// 左侧公司信息
.footer-company {
    display: flex;
    gap: 20px;

    .company-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-shrink: 0;

        .logo-image {
            width: 138px;
            height: 139px;
            margin-bottom: 8px;
            border-radius: 4px;
        }
    }

    .company-text {
        width: 312px;
        h6 {
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            margin: 0 0 10px 0;
        }

        .company-divider {
            height: 1px;
            background-color: rgba(255, 255, 255, 0.5);
            margin-bottom: 10px;
        }

        .company-description {
            font-size: 12px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: 30px;
        }

        .company-more {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #fff;
            cursor: pointer;

            &:hover {
                color: #00bfff;
            }

            .el-icon {
                font-size: 12px;
            }
        }
    }
}

// 中间链接区域
.footer-links {
    display: flex;
    gap: 80px;
    flex: 1;
    justify-content: center;
}

.link-group {
    .group-title {
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 10px;
    }

    .group-divider {
        width: 192px;
        height: 1px;
        background-color: rgba(255, 255, 255, 0.5);
        margin-bottom: 10px;
    }

    .group-divider--contact {
        width: 251px;
    }

    .link-columns {
        display: flex;
        gap: 60px;
    }

    .link-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
            margin-bottom: 8px;

            a {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.5);
                text-decoration: none;
                transition: color 0.3s ease;

                &:hover {
                    color: #00bfff;
                }
            }
        }
    }

    .contact-info {
        .contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            gap: 8px;

            .contact-icon {
                font-size: 16px;
                color: #cccccc;
                margin-top: 2px;
                flex-shrink: 0;
            }

            span {
                flex: 1;
                line-height: 1.4;
            }
        }
    }
}

// 右侧二维码区域
.footer-qrcodes {
    display: flex;
    gap: 40px;
    align-items: flex-start;
}

.qrcode-item {
    text-align: center;

    .qrcode-label {
        font-size: 16px;
        color: #fff;
        display: block;
        margin-bottom: 10px;
    }

    .qrcode-image {
        width: 120px;
        height: 115px;
        display: block;
    }
}

// 底部版权信息
.footer-bottom {
    margin-top: 96px;
    text-align: center;

    .copyright {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        margin: 0;
    }
}
</style>
