<template>
    <div class="product-item" @click="handleClick">
        <!-- 产品图片 -->
        <div class="product-item__image">
            <img :src="product.image || defaultImage" :alt="product.name" />
        </div>

        <!-- 产品信息 -->
        <div class="product-item__content">
            <h4 class="product-item__title">{{ product.name }}</h4>

            <!-- 标签 -->
            <div class="product-item__tags">
                <span v-for="tag in product.tags" :key="tag.type" :class="['tag', `tag--${tag.type}`]">
                    {{ tag.text }}
                </span>
                <!-- 默认标签 -->
                <span v-if="!product.tags || product.tags.length === 0" class="tag tag--free-shipping">包邮</span>
                <span v-if="!product.tags || product.tags.length === 0" class="tag tag--in-stock">现货</span>
            </div>

            <!-- 价格和销量 -->
            <div class="product-item__bottom">
                <div class="product-item__price">
                    <span class="price-symbol">¥</span>
                    <span class="price-integer">{{ Math.floor(product.price) }}</span>
                    <span class="price-decimal">.{{ (product.price % 1).toFixed(2).slice(2) }}</span>
                </div>
                <div class="product-item__sales">{{ product.sales || '已售400+' }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
// Props
const props = defineProps({
    product: {
        type: Object,
        required: true,
        validator: (product) => {
            return product && typeof product.name === 'string' && typeof product.price === 'number';
        },
    },
    defaultImage: {
        type: String,
        default: '~/assets/images/home/<USER>',
    },
});

// 事件处理
const emit = defineEmits(['click']);

const handleClick = () => {
    emit('click', props.product);
};
</script>

<style lang="scss" scoped>
// 产品项
.product-item {
    border-radius: 8px;
    padding: 4px;
    cursor: pointer;
    background-color: #fff;
    border: 2px solid transparent;
    box-sizing: border-box;
    transition: all 0.3s ease;

    &:hover {
        border-color: #f53f3f;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    }

    &__image {
        position: relative;
        height: 198px;
        background-color: #f7f8fc;
        border-radius: 6px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
    }

    &__content {
        padding: 0 4px;
        text-align: left;
    }

    &__title {
        font-size: 14px;
        font-weight: 500;
        color: #1d2129;
        margin-bottom: 8px;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__tags {
        display: flex;
        gap: 6px;
        margin-bottom: 8px;
        flex-wrap: wrap;
    }

    &__bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    &__price {
        color: #f53f3f;
        font-weight: 700;
        display: flex;
        align-items: baseline;

        .price-symbol {
            font-size: 12px;
            margin-right: 2px;
            line-height: 1;
        }

        .price-integer {
            font-size: 18px;
            font-weight: 800;
            line-height: 1;
        }

        .price-decimal {
            font-size: 14px;
            font-weight: 700;
            line-height: 1;
        }
    }

    &__sales {
        font-size: 12px;
        color: #86909c;
    }
}

.tag {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: 500;

    &--free-shipping {
        background-color: #fff7e8;
        color: #ff7d00;
    }

    &--in-stock {
        background-color: #e8ffea;
        color: #2ba471;
    }

    &--hot {
        background-color: #ffebee;
        color: #f44336;
    }

    &--new {
        background-color: #e3f2fd;
        color: #2196f3;
    }

    &--discount {
        background-color: #fce4ec;
        color: #e91e63;
    }
}
</style>
