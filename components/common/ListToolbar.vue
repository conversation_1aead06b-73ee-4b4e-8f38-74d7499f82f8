<template>
    <div class="list-toolbar">
        <div class="list-toolbar__left">
            <!-- 结果统计 -->
            <div class="list-toolbar__stats">
                <span class="stats-text">
                    共找到 <strong>{{ total }}</strong> 个结果
                </span>
            </div>
        </div>

        <div class="list-toolbar__right">
            <!-- 排序选择 -->
            <div class="list-toolbar__sort">
                <span class="sort-label">排序：</span>
                <el-select
                    :model-value="currentSort"
                    @update:model-value="handleSortChange"
                    size="small"
                    :disabled="loading"
                >
                    <el-option
                        v-for="option in sortOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
            </div>

            <!-- 视图切换 -->
            <div class="list-toolbar__view">
                <el-button-group>
                    <el-button
                        :type="currentView === 'grid' ? 'primary' : 'default'"
                        size="small"
                        @click="handleViewChange('grid')"
                        :disabled="loading"
                    >
                        <el-icon><Grid /></el-icon>
                        网格
                    </el-button>
                    <el-button
                        :type="currentView === 'list' ? 'primary' : 'default'"
                        size="small"
                        @click="handleViewChange('list')"
                        :disabled="loading"
                    >
                        <el-icon><List /></el-icon>
                        列表
                    </el-button>
                </el-button-group>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Grid, List } from '@element-plus/icons-vue'

// Props
const props = defineProps({
    total: {
        type: Number,
        default: 0
    },
    currentView: {
        type: String,
        default: 'grid',
        validator: (value) => ['grid', 'list'].includes(value)
    },
    sortOptions: {
        type: Array,
        default: () => [
            { label: '综合排序', value: 'default' },
            { label: '价格从低到高', value: 'price_asc' },
            { label: '价格从高到低', value: 'price_desc' },
            { label: '最新发布', value: 'created_desc' }
        ]
    },
    currentSort: {
        type: String,
        default: 'default'
    },
    loading: {
        type: Boolean,
        default: false
    }
})

// 事件
const emit = defineEmits(['view-change', 'sort-change'])

// 方法
const handleViewChange = (view) => {
    if (view !== props.currentView) {
        emit('view-change', view)
    }
}

const handleSortChange = (sort) => {
    if (sort !== props.currentSort) {
        emit('sort-change', sort)
    }
}
</script>

<style lang="scss" scoped>
.list-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    
    &__left {
        display: flex;
        align-items: center;
    }
    
    &__right {
        display: flex;
        align-items: center;
        gap: 16px;
    }
    
    &__stats {
        .stats-text {
            font-size: 14px;
            color: #4e5969;
            
            strong {
                color: #1d2129;
                font-weight: 600;
            }
        }
    }
    
    &__sort {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .sort-label {
            font-size: 14px;
            color: #4e5969;
            white-space: nowrap;
        }
        
        :deep(.el-select) {
            width: 140px;
        }
    }
    
    &__view {
        :deep(.el-button-group) {
            .el-button {
                padding: 8px 12px;
                
                .el-icon {
                    margin-right: 4px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .list-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
        
        &__left,
        &__right {
            justify-content: center;
        }
        
        &__right {
            flex-direction: column;
            gap: 12px;
        }
        
        &__sort,
        &__view {
            justify-content: center;
        }
    }
}
</style>
