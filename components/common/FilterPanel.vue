<template>
    <div class="filter-panel">
        <div class="filter-panel__header">
            <h3 class="filter-panel__title">筛选条件</h3>
            <el-button type="text" size="small" @click="clearAllFilters" :disabled="!hasActiveFilters"> 清空筛选 </el-button>
        </div>

        <div class="filter-panel__content">
            <!-- 分类筛选 -->
            <div v-if="config.categories" class="filter-group">
                <h4 class="filter-group__title">商品分类</h4>
                <div class="filter-group__content">
                    <el-checkbox-group v-model="selectedCategories" @change="handleCategoryChange">
                        <el-checkbox v-for="category in config.categories" :key="category.value || category" :label="category.value || category" class="filter-checkbox">
                            {{ category.label || category }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>

            <!-- 价格范围筛选 -->
            <div v-if="config.priceRange" class="filter-group">
                <h4 class="filter-group__title">价格范围</h4>
                <div class="filter-group__content">
                    <div class="price-range">
                        <el-input v-model="priceRange.min" placeholder="最低价" type="number" size="small" @change="handlePriceChange">
                            <template #prepend>¥</template>
                        </el-input>
                        <span class="price-separator">-</span>
                        <el-input v-model="priceRange.max" placeholder="最高价" type="number" size="small" @change="handlePriceChange">
                            <template #prepend>¥</template>
                        </el-input>
                    </div>
                    <!-- 快速价格选择 -->
                    <div class="price-quick-select" v-if="config.priceRanges">
                        <el-button v-for="range in config.priceRanges" :key="range.label" size="small" :type="isPriceRangeSelected(range) ? 'primary' : 'default'" @click="selectPriceRange(range)">
                            {{ range.label }}
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 品牌筛选 -->
            <div v-if="config.brands" class="filter-group">
                <h4 class="filter-group__title">品牌</h4>
                <div class="filter-group__content">
                    <div class="brand-search" v-if="config.brands.length > 10">
                        <el-input v-model="brandSearchKeyword" placeholder="搜索品牌" size="small" clearable>
                            <template #prefix>
                                <el-icon><Search /></el-icon>
                            </template>
                        </el-input>
                    </div>
                    <el-checkbox-group v-model="selectedBrands" @change="handleBrandChange">
                        <el-checkbox v-for="brand in filteredBrands" :key="brand.value || brand" :label="brand.value || brand" class="filter-checkbox">
                            {{ brand.label || brand }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>

            <!-- 自定义筛选器 -->
            <div v-for="customFilter in config.customFilters" :key="customFilter.key" class="filter-group">
                <h4 class="filter-group__title">{{ customFilter.label }}</h4>
                <div class="filter-group__content">
                    <!-- 单选类型 -->
                    <el-radio-group v-if="customFilter.type === 'radio'" v-model="customFilterValues[customFilter.key]" @change="handleCustomFilterChange">
                        <el-radio v-for="option in customFilter.options" :key="option.value" :label="option.value" class="filter-radio">
                            {{ option.label }}
                        </el-radio>
                    </el-radio-group>

                    <!-- 多选类型 -->
                    <el-checkbox-group v-else-if="customFilter.type === 'checkbox'" v-model="customFilterValues[customFilter.key]" @change="handleCustomFilterChange">
                        <el-checkbox v-for="option in customFilter.options" :key="option.value" :label="option.value" class="filter-checkbox">
                            {{ option.label }}
                        </el-checkbox>
                    </el-checkbox-group>

                    <!-- 范围选择类型 -->
                    <el-slider v-else-if="customFilter.type === 'range'" v-model="customFilterValues[customFilter.key]" :min="customFilter.min || 0" :max="customFilter.max || 100" :step="customFilter.step || 1" :show-tooltip="true" range @change="handleCustomFilterChange" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue';

// Props
const props = defineProps({
    config: {
        type: Object,
        default: () => ({}),
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

// 事件
const emit = defineEmits(['filter-change']);

// 响应式数据
const selectedCategories = ref([]);
const selectedBrands = ref([]);
const priceRange = reactive({
    min: '',
    max: '',
});
const brandSearchKeyword = ref('');
const customFilterValues = reactive({});

// 计算属性
const hasActiveFilters = computed(() => {
    return selectedCategories.value.length > 0 || selectedBrands.value.length > 0 || priceRange.min !== '' || priceRange.max !== '' || Object.values(customFilterValues).some((value) => (Array.isArray(value) ? value.length > 0 : value !== '' && value !== null));
});

const filteredBrands = computed(() => {
    if (!brandSearchKeyword.value) {
        return props.config.brands || [];
    }
    return (props.config.brands || []).filter((brand) => {
        const brandName = brand.label || brand;
        return brandName.toLowerCase().includes(brandSearchKeyword.value.toLowerCase());
    });
});

// 方法
const clearAllFilters = () => {
    selectedCategories.value = [];
    selectedBrands.value = [];
    priceRange.min = '';
    priceRange.max = '';
    brandSearchKeyword.value = '';

    // 清空自定义筛选器
    Object.keys(customFilterValues).forEach((key) => {
        const filter = props.config.customFilters?.find((f) => f.key === key);
        if (filter?.type === 'checkbox') {
            customFilterValues[key] = [];
        } else {
            customFilterValues[key] = filter?.type === 'range' ? [filter.min || 0, filter.max || 100] : '';
        }
    });

    emitFilterChange();
};

const emitFilterChange = () => {
    const filters = {
        categories: selectedCategories.value,
        brands: selectedBrands.value,
        priceRange: {
            min: priceRange.min ? Number(priceRange.min) : null,
            max: priceRange.max ? Number(priceRange.max) : null,
        },
        custom: { ...customFilterValues },
    };

    emit('filter-change', filters);
};

const handleCategoryChange = () => {
    emitFilterChange();
};

const handleBrandChange = () => {
    emitFilterChange();
};

const handlePriceChange = () => {
    emitFilterChange();
};

const handleCustomFilterChange = () => {
    emitFilterChange();
};

const isPriceRangeSelected = (range) => {
    return priceRange.min == range.min && priceRange.max == range.max;
};

const selectPriceRange = (range) => {
    priceRange.min = range.min;
    priceRange.max = range.max;
    handlePriceChange();
};

// 初始化自定义筛选器值
watch(
    () => props.config.customFilters,
    (newFilters) => {
        if (newFilters) {
            newFilters.forEach((filter) => {
                if (!(filter.key in customFilterValues)) {
                    if (filter.type === 'checkbox') {
                        customFilterValues[filter.key] = [];
                    } else if (filter.type === 'range') {
                        customFilterValues[filter.key] = [filter.min || 0, filter.max || 100];
                    } else {
                        customFilterValues[filter.key] = '';
                    }
                }
            });
        }
    },
    { immediate: true }
);
</script>

<style lang="scss" scoped>
.filter-panel {
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    &__title {
        font-size: 16px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }

    &__content {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        align-items: flex-start;
    }
}

.filter-group {
    min-width: 200px;
    flex-shrink: 0;

    &__title {
        font-size: 14px;
        font-weight: 500;
        color: #1d2129;
        margin: 0 0 12px 0;
        white-space: nowrap;
    }

    &__content {
        .filter-checkbox,
        .filter-radio {
            display: block;
            margin-bottom: 8px;

            :deep(.el-checkbox__label),
            :deep(.el-radio__label) {
                font-size: 14px;
                color: #4e5969;
            }
        }
    }
}

.price-range {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .price-separator {
        color: #86909c;
        font-size: 14px;
    }

    :deep(.el-input) {
        flex: 1;
    }
}

.price-quick-select {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .el-button {
        font-size: 12px;
        padding: 4px 8px;
    }
}

.brand-search {
    margin-bottom: 12px;
}

// Element Plus 组件样式覆盖
:deep(.el-checkbox-group),
:deep(.el-radio-group) {
    display: flex;
    flex-direction: column;
}

:deep(.el-slider) {
    margin: 12px 0;
}

:deep(.el-button--text) {
    color: #3b82f6;

    &:hover {
        color: #2563eb;
    }

    &.is-disabled {
        color: #c9cdd4;
    }
}
</style>
