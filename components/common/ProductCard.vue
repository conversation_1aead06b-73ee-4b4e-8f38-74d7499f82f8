<template>
  <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer group">
    <!-- 商品图片 -->
    <div class="relative overflow-hidden rounded-t-lg">
      <NuxtImg
        :src="product.image"
        :alt="product.name"
        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        loading="lazy"
      />
      <!-- 收藏按钮 -->
      <button 
        class="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
        @click.stop="toggleFavorite"
      >
        <el-icon :class="{ 'text-red-500': isFavorite, 'text-gray-400': !isFavorite }">
          <Star />
        </el-icon>
      </button>
      <!-- 折扣标签 -->
      <div 
        v-if="discountPercent > 0"
        class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold"
      >
        -{{ discountPercent }}%
      </div>
    </div>

    <!-- 商品信息 -->
    <div class="p-4">
      <!-- 商品名称 -->
      <h3 
        class="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors"
        @click="goToProduct"
      >
        {{ product.name }}
      </h3>

      <!-- 评分和销量 -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-1">
          <el-rate
            v-model="product.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
            size="small"
          />
        </div>
        <span class="text-xs text-gray-500">已售{{ product.sales }}</span>
      </div>

      <!-- 价格 -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <span class="text-lg font-bold text-red-600">
            ¥{{ formatPrice(product.price) }}
          </span>
          <span 
            v-if="product.originalPrice && product.originalPrice > product.price"
            class="text-sm text-gray-400 line-through"
          >
            ¥{{ formatPrice(product.originalPrice) }}
          </span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-2">
        <el-button 
          type="primary" 
          size="small" 
          class="flex-1"
          @click.stop="addToCart"
        >
          <el-icon class="mr-1"><ShoppingCart /></el-icon>
          加入购物车
        </el-button>
        <el-button 
          size="small" 
          @click.stop="goToProduct"
        >
          查看详情
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Star, ShoppingCart } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义props
interface Product {
  id: number
  name: string
  price: number
  originalPrice?: number
  image: string
  rating: number
  sales: number
}

const props = defineProps<{
  product: Product
}>()

// 响应式数据
const isFavorite = ref(false)

// 计算属性
const discountPercent = computed(() => {
  if (props.product.originalPrice && props.product.originalPrice > props.product.price) {
    return Math.round((1 - props.product.price / props.product.originalPrice) * 100)
  }
  return 0
})

// 路由
const router = useRouter()

// 方法
const formatPrice = (price: number) => {
  return price.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  ElMessage({
    message: isFavorite.value ? '已添加到收藏' : '已取消收藏',
    type: 'success',
    duration: 2000
  })
}

const addToCart = () => {
  // 这里可以调用购物车store的方法
  // const cartStore = useCartStore()
  // cartStore.addItem(props.product)
  
  ElMessage({
    message: '已添加到购物车',
    type: 'success',
    duration: 2000
  })
}

const goToProduct = () => {
  router.push(`/product/${props.product.id}`)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
