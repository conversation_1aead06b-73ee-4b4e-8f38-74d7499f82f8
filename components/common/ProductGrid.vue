<template>
    <div class="product-grid" :class="[`product-grid--${viewMode}`]">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="product-grid__grid">
            <CommonProductItem
                v-for="item in items"
                :key="item.id"
                :product="item"
                @click="handleItemClick"
            />
        </div>

        <!-- 列表视图 -->
        <div v-else class="product-grid__list">
            <div
                v-for="item in items"
                :key="item.id"
                class="product-list-item"
                @click="handleItemClick(item)"
            >
                <!-- 商品图片 -->
                <div class="product-list-item__image">
                    <img :src="item.image || defaultImage" :alt="item.name" />
                </div>

                <!-- 商品信息 -->
                <div class="product-list-item__content">
                    <div class="product-list-item__main">
                        <h4 class="product-list-item__title">{{ item.name }}</h4>
                        <p class="product-list-item__description" v-if="item.description">
                            {{ item.description }}
                        </p>
                        
                        <!-- 标签 -->
                        <div class="product-list-item__tags" v-if="item.tags && item.tags.length">
                            <span 
                                v-for="tag in item.tags" 
                                :key="tag.type || tag" 
                                :class="['tag', `tag--${tag.type || 'default'}`]"
                            >
                                {{ tag.text || tag }}
                            </span>
                        </div>
                    </div>

                    <!-- 价格和操作 -->
                    <div class="product-list-item__footer">
                        <div class="product-list-item__price">
                            <span class="price-symbol">¥</span>
                            <span class="price-integer">{{ Math.floor(item.price) }}</span>
                            <span class="price-decimal">.{{ (item.price % 1).toFixed(2).slice(2) }}</span>
                        </div>
                        
                        <div class="product-list-item__actions">
                            <el-button size="small" type="primary">
                                <el-icon><ShoppingCart /></el-icon>
                                加入购物车
                            </el-button>
                            <el-button size="small">查看详情</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ShoppingCart } from '@element-plus/icons-vue'

// Props
const props = defineProps({
    items: {
        type: Array,
        default: () => []
    },
    viewMode: {
        type: String,
        default: 'grid',
        validator: (value) => ['grid', 'list'].includes(value)
    },
    loading: {
        type: Boolean,
        default: false
    },
    defaultImage: {
        type: String,
        default: '~/assets/images/home/<USER>'
    }
})

// 事件
const emit = defineEmits(['item-click'])

// 方法
const handleItemClick = (item) => {
    emit('item-click', item)
}
</script>

<style lang="scss" scoped>
.product-grid {
    &__grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 16px;
        
        @media (max-width: 1200px) {
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        }
        
        @media (max-width: 768px) {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 12px;
        }
    }
    
    &__list {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }
}

// 列表视图样式
.product-list-item {
    display: flex;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    &__image {
        width: 120px;
        height: 120px;
        flex-shrink: 0;
        background-color: #f7f8fc;
        border-radius: 6px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        
        img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
    }
    
    &__content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    &__main {
        flex: 1;
    }
    
    &__title {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
        margin: 0 0 8px 0;
        line-height: 1.4;
    }
    
    &__description {
        font-size: 14px;
        color: #4e5969;
        line-height: 1.5;
        margin: 0 0 12px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    &__tags {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        margin-bottom: 12px;
    }
    
    &__footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    &__price {
        color: #f53f3f;
        font-weight: 700;
        display: flex;
        align-items: baseline;
        
        .price-symbol {
            font-size: 14px;
            margin-right: 2px;
        }
        
        .price-integer {
            font-size: 20px;
            font-weight: 800;
        }
        
        .price-decimal {
            font-size: 16px;
            font-weight: 700;
        }
    }
    
    &__actions {
        display: flex;
        gap: 8px;
        
        .el-button {
            .el-icon {
                margin-right: 4px;
            }
        }
    }
}

// 标签样式
.tag {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: 500;
    
    &--free-shipping {
        background-color: #fff7e8;
        color: #ff7d00;
    }
    
    &--in-stock {
        background-color: #e8ffea;
        color: #2ba471;
    }
    
    &--hot {
        background-color: #ffebee;
        color: #f44336;
    }
    
    &--new {
        background-color: #e3f2fd;
        color: #2196f3;
    }
    
    &--discount {
        background-color: #fce4ec;
        color: #e91e63;
    }
    
    &--default {
        background-color: #f0f0f0;
        color: #666;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .product-list-item {
        flex-direction: column;
        
        &__image {
            width: 100%;
            height: 200px;
            margin-right: 0;
            margin-bottom: 12px;
        }
        
        &__footer {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
        }
        
        &__actions {
            justify-content: center;
        }
    }
}
</style>
