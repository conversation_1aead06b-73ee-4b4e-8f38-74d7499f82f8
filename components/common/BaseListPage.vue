<template>
    <div class="base-list-page">
        <div class="base-list-page__content">
            <!-- 筛选面板 -->
            <div class="base-list-page__filters" v-if="showFilters">
                <CommonFilterPanel :config="filterConfig" :loading="loading" @filter-change="handleFilterChange" />
            </div>

            <!-- 主内容区域 -->
            <main class="base-list-page__main">
                <!-- 列表工具栏 -->
                <CommonListToolbar :total="total" :current-view="currentView" :sort-options="sortOptions" :current-sort="currentSort" :loading="loading" @view-change="handleViewChange" @sort-change="handleSortChange" />

                <!-- 列表内容区域 -->
                <div class="base-list-page__list-container">
                    <!-- 加载状态 -->
                    <div v-if="loading" class="base-list-page__loading">
                        <el-skeleton :rows="8" animated />
                    </div>

                    <!-- 空状态 -->
                    <div v-else-if="!items.length" class="base-list-page__empty">
                        <el-empty :description="emptyText" />
                    </div>

                    <!-- 商品网格 -->
                    <CommonProductGrid v-else :items="items" :view-mode="currentView" :loading="loading" @item-click="handleItemClick" />
                </div>

                <!-- 分页组件 -->
                <div class="base-list-page__pagination" v-if="showPagination && total > 0">
                    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="pageSizes" :total="total" :background="true" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handlePageChange" />
                </div>
            </main>
        </div>
    </div>
</template>

<script setup>
// Props 定义
const props = defineProps({
    // 筛选配置
    showFilters: {
        type: Boolean,
        default: true,
    },
    filterConfig: {
        type: Object,
        default: () => ({}),
    },

    // 列表配置
    items: {
        type: Array,
        default: () => [],
    },
    total: {
        type: Number,
        default: 0,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    emptyText: {
        type: String,
        default: '暂无数据',
    },

    // 分页配置
    showPagination: {
        type: Boolean,
        default: true,
    },
    pageSizes: {
        type: Array,
        default: () => [20, 40, 60, 100],
    },

    // 排序配置
    sortOptions: {
        type: Array,
        default: () => [
            { label: '综合排序', value: 'default' },
            { label: '价格从低到高', value: 'price_asc' },
            { label: '价格从高到低', value: 'price_desc' },
            { label: '最新发布', value: 'created_desc' },
        ],
    },
});

// 事件定义
const emit = defineEmits(['filter-change', 'sort-change', 'view-change', 'page-change', 'size-change', 'item-click']);

// 响应式数据
const currentView = ref('grid'); // grid | list
const currentSort = ref('default');
const currentPage = ref(1);
const pageSize = ref(20);

// 事件处理函数
const handleFilterChange = (filters) => {
    currentPage.value = 1; // 筛选时重置到第一页
    emit('filter-change', filters);
};

const handleSortChange = (sort) => {
    currentSort.value = sort;
    currentPage.value = 1; // 排序时重置到第一页
    emit('sort-change', sort);
};

const handleViewChange = (view) => {
    currentView.value = view;
    emit('view-change', view);
};

const handlePageChange = (page) => {
    currentPage.value = page;
    emit('page-change', page);
};

const handleSizeChange = (size) => {
    pageSize.value = size;
    currentPage.value = 1; // 改变页面大小时重置到第一页
    emit('size-change', size);
};

const handleItemClick = (item) => {
    emit('item-click', item);
};

// 暴露给父组件的方法
defineExpose({
    resetPage: () => {
        currentPage.value = 1;
    },
    getCurrentPage: () => currentPage.value,
    getPageSize: () => pageSize.value,
    getCurrentSort: () => currentSort.value,
    getCurrentView: () => currentView.value,
});
</script>

<style lang="scss" scoped>
.base-list-page {
    min-height: 100vh;
    background-color: #f5f5f5;

    &__content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    &__filters {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &__main {
        flex: 1;
        min-width: 0;
    }

    &__list-container {
        margin: 16px 0;
        min-height: 400px;
    }

    &__loading {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
    }

    &__empty {
        background-color: #fff;
        padding: 60px 20px;
        border-radius: 8px;
        text-align: center;
    }

    &__pagination {
        display: flex;
        justify-content: center;
        padding: 24px 0;
        background-color: #fff;
        border-radius: 8px;
        margin-top: 16px;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .base-list-page {
        &__content {
            padding: 0 16px;
        }

        &__filters {
            padding: 16px;
        }
    }
}
</style>
