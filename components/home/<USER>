<template>
    <div class="home-stats">
        <div class="home-stats__container">
            <div v-for="(stat, index) in stats" :key="stat.id" class="home-stats__item" :class="{ 'home-stats__item--animate': isVisible }" :style="{ animationDelay: `${index * 0.1}s` }">
                <!-- 图标 -->
                <div class="home-stats__icon">
                    <img :src="stat.icon" :alt="stat.title" />
                </div>

                <div>
                    <!-- 数值 -->
                    <div class="home-stats__value">{{ animatedValues[stat.id] || 0 }}{{ stat.suffix }}</div>

                    <!-- 标题 -->
                    <div class="home-stats__title">
                        {{ stat.title }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 响应式数据
const isVisible = ref(false);
const animatedValues = reactive({});

// 统计数据
const stats = reactive([
    {
        id: 1,
        title: '工厂合作商',
        value: 10000,
        suffix: '+',
        icon: '/images/stats/factory-icon.png',
    },
    {
        id: 2,
        title: '行业覆盖',
        value: 25,
        suffix: '',
        icon: '/images/stats/industry-icon.png',
    },
    {
        id: 3,
        title: '项目案例数',
        value: 10000,
        suffix: '+',
        icon: '/images/stats/project-icon.png',
    },
    {
        id: 4,
        title: '专利数量',
        value: 890,
        suffix: '',
        icon: '/images/stats/patent-icon.png',
    },
]);

// 数字动画配置
const animationConfig = reactive({
    duration: 2000, // 动画持续时间
    easing: 'easeOutQuart', // 缓动函数
});

// 缓动函数
const easeOutQuart = (t) => {
    return 1 - Math.pow(1 - t, 4);
};

// 数字动画函数
const animateValue = (stat) => {
    const startTime = Date.now();
    const startValue = 0;
    const endValue = stat.value;

    const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / animationConfig.duration, 1);

        const easedProgress = easeOutQuart(progress);
        const currentValue = Math.floor(startValue + (endValue - startValue) * easedProgress);

        animatedValues[stat.id] = currentValue;

        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    };

    animate();
};

// 启动所有数字动画
const startAnimations = () => {
    stats.forEach((stat, index) => {
        setTimeout(() => {
            animateValue(stat);
        }, index * 100); // 错开动画时间
    });
};

// 使用 Intersection Observer 检测组件是否进入视口
const setupIntersectionObserver = () => {
    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting && !isVisible.value) {
                    isVisible.value = true;
                    startAnimations();
                }
            });
        },
        {
            threshold: 0.5, // 当50%的组件可见时触发
        }
    );

    return observer;
};

// 生命周期
onMounted(() => {
    const observer = setupIntersectionObserver();
    const element = document.querySelector('.home-stats');
    if (element) {
        observer.observe(element);
    }

    // 如果不支持 Intersection Observer，直接启动动画
    if (!window.IntersectionObserver) {
        isVisible.value = true;
        startAnimations();
    }
});
</script>

<style scoped lang="scss">
.home-stats {
    background-color: #fff;
    border-radius: 12px;

    &__container {
        display: flex;
        align-items: center;
        height: 118px;
        background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, #e6f4ff 50%, rgba(247, 248, 252, 0) 100%);
    }

    &__item {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 1px;
            height: 33px;
            background-color: #0783ff;
            transform: translateY(-50%);
        }

        &:first-child::after {
            display: none;
        }
    }

    &__icon {
        width: 80px;
        height: 80px;
        margin-right: 10px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    &__value {
        font-size: 29px;
        font-weight: 600;
        margin-bottom: 8px;
        line-height: 1;
        background: linear-gradient(225.64deg, #0578ff 0%, #15d5ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-fill-color: transparent;
    }

    &__title {
        font-size: 13px;
        color: #1d2129;
        font-weight: 400;
        text-align: left;
    }
}
</style>
