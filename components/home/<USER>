<template>
    <div class="hero-banner">
        <el-carousel class="hero-banner__carousel" :interval="autoPlayConfig.interval" :autoplay="autoPlayConfig.enabled" arrow="never" indicator-position="outside" height="404px" :loop="true" @change="handleSlideChange">
            <el-carousel-item v-for="slide in slides" :key="slide.id" class="hero-banner__slide">
                <div class="hero-banner__slide-bg" :style="{ backgroundImage: `url(${slide.image})` }"></div>
            </el-carousel-item>
        </el-carousel>
    </div>
</template>

<script setup>
// 响应式数据
const currentSlide = ref(0);

// 轮播图数据
const slides = reactive([
    {
        id: 1,
        image: '/images/home/<USER>',
    },
    {
        id: 2,
        image: '/images/home/<USER>',
    },
    {
        id: 3,
        image: '/images/home/<USER>',
    },
]);

// 自动播放配置
const autoPlayConfig = reactive({
    enabled: true,
    interval: 5000,
});

// 事件处理
const emit = defineEmits(['slide-change']);

const handleSlideChange = (index) => {
    currentSlide.value = index;
    emit('slide-change', index);
};
</script>

<style lang="scss" scoped>
// 英雄轮播图
.hero-banner {
    width: 100%;

    // 轮播容器
    &__carousel {
        border-radius: 12px;
        overflow: hidden;

        // 确保Element Plus轮播图容器正确显示
        :deep(.el-carousel__container) {
            height: 404px;
        }

        // 确保轮播项占满容器
        :deep(.el-carousel__item) {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
    }

    // 轮播项
    &__slide {
        position: relative;
        width: 100%;
        height: 100%;
        display: block;
    }

    // 背景图片
    &__slide-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: 100% 100%; // 强制100%宽高，可能会拉伸
        background-position: center;
        background-repeat: no-repeat;

        // 如果没有背景图片，显示默认颜色
        background-color: #f0f0f0;
    }
}

// Element Plus轮播图全局样式修复
:deep(.el-carousel) {
    height: 404px;

    .el-carousel__container {
        height: 404px;
        position: relative;
        overflow: hidden;
    }

    .el-carousel__item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: all 0.4s ease-in-out;

        &.is-active {
            opacity: 1;
            z-index: 2;
        }

        &.is-in-stage {
            opacity: 1;
            z-index: 1;
        }

        &.is-animating {
            transition: all 0.4s ease-in-out;
        }
    }
}

// Element Plus轮播图指示器样式
:deep(.el-carousel__indicators) {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    gap: 8px;
}

:deep(.el-carousel__indicator) {
    .el-carousel__button {
        background-color: rgba(255, 255, 255, 0.6);
        border: none;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
            background-color: rgba(255, 255, 255, 0.8);
        }
    }

    &.is-active .el-carousel__button {
        background-color: #ffffff;
        transform: scale(1.2);
    }
}
</style>
