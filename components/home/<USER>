<template>
    <HomeProductSection
        title="产品直供"
        subtitle="源头货源全网最低价"
        description="推动生产全要素与数据智能融合，重塑生产价值链，让生产看得见"
        :show-brand="true"
        more-button-text="查看更多"
        intro-image="/images/home/<USER>"
        introBackgroundColor="linear-gradient( 180deg, #2382EC 5%, rgba(216,216,216,0) 100%);"
        :tabs="tabs"
        :products="products"
        @product-click="handleProductClick"
        @more-click="handleMoreClick"
    />
</template>

<script setup>
// ProductSection 组件会自动导入为 HomeProductSection;

// 标签页配置
const tabs = reactive([
    { id: 'ups-power', name: 'UPS电源' },
    { id: 'eps-power', name: 'EPS电源' },
    { id: 'battery-group', name: '电池组' },
    { id: 'battery', name: '蓄电池' },
    { id: 'battery-cabinet', name: '电池柜' },
]);

// 产品数据
const products = reactive({
    'ups-power': [
        {
            id: 1,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
            isHot: true,
        },
        {
            id: 2,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
        {
            id: 3,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
        {
            id: 4,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
        {
            id: 5,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
        {
            id: 6,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
        {
            id: 7,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
        {
            id: 8,
            name: '台达UPS',
            subtitle: '单相双变换 智能管理',
            price: 5666.0,
        },
    ],
    'eps-power': [
        // EPS电源产品数据...
    ],
    'battery-group': [
        // 电池组产品数据...
    ],
    battery: [
        // 蓄电池产品数据...
    ],
    'battery-cabinet': [
        // 电池柜产品数据...
    ],
});

// 事件处理
const emit = defineEmits(['product-click', 'more-click']);

const handleProductClick = (product) => {
    emit('product-click', product);
};

const handleMoreClick = (data) => {
    emit('more-click', data);
    navigateTo(`/products?section=${data.section}&tab=${data.tab}`);
};
</script>
