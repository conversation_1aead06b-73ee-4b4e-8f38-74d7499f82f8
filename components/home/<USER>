<template>
    <nav class="home-nav">
        <div class="home-nav__list">
            <!-- 首页 -->
            <NuxtLink to="/" class="home-nav__item home-nav__item--active">
                <img src="~/assets/images/home/<USER>" alt="首页图标" class="home-nav__icon" />
                <span class="home-nav__text">首页</span>
            </NuxtLink>

            <!-- 成套方案（下拉） -->
            <el-dropdown trigger="hover" class="home-nav__dropdown">
                <div class="home-nav__item home-nav__item--with-badge" data-badge="上千套完整方案">
                    <img src="~/assets/images/home/<USER>" alt="成套方案图标" class="home-nav__icon" />
                    <span class="home-nav__text">成套方案</span>
                    <el-icon class="home-nav__arrow"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="handleDropdownClick('solutions', 'datacenter')">数据中心方案</el-dropdown-item>
                        <el-dropdown-item @click="handleDropdownClick('solutions', 'security')">安防监控方案</el-dropdown-item>
                        <el-dropdown-item @click="handleDropdownClick('solutions', 'transport')">交通运输方案</el-dropdown-item>
                        <el-dropdown-item @click="handleDropdownClick('solutions', 'emergency')">应急通信方案</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>

            <!-- 商城单品（下拉） -->
            <el-dropdown trigger="hover" class="home-nav__dropdown">
                <div class="home-nav__item home-nav__item--with-badge" data-badge="全网商品最优">
                    <img src="~/assets/images/home/<USER>" alt="商城单品图标" class="home-nav__icon" />
                    <span class="home-nav__text">商城单品</span>
                    <el-icon class="home-nav__arrow"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="handleDropdownClick('products', 'ups')">UPS电源</el-dropdown-item>
                        <el-dropdown-item @click="handleDropdownClick('products', 'eps')">EPS电源</el-dropdown-item>
                        <el-dropdown-item @click="handleDropdownClick('products', 'battery')">蓄电池</el-dropdown-item>
                        <el-dropdown-item @click="handleDropdownClick('products', 'cabinet')">配电柜</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>

            <!-- 其他导航项 -->
            <NuxtLink to="/partners" class="home-nav__item">
                <img src="~/assets/images/home/<USER>" alt="合作商家图标" class="home-nav__icon" />
                <span class="home-nav__text">合作商家</span>
            </NuxtLink>

            <NuxtLink to="/wiki" class="home-nav__item">
                <img src="~/assets/images/home/<USER>" alt="瑞物百科图标" class="home-nav__icon" />
                <span class="home-nav__text">瑞物百科</span>
            </NuxtLink>

            <NuxtLink to="/join" class="home-nav__item home-nav__item--with-badge" data-badge="享受商家权益">
                <img src="~/assets/images/home/<USER>" alt="入驻我们图标" class="home-nav__icon" />
                <span class="home-nav__text">入驻我们</span>
            </NuxtLink>

            <NuxtLink to="/about" class="home-nav__item">
                <img src="~/assets/images/home/<USER>" alt="关于我们图标" class="home-nav__icon" />
                <span class="home-nav__text">关于我们</span>
            </NuxtLink>
        </div>
    </nav>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue';

// 定义事件
const emit = defineEmits(['nav-click', 'dropdown-click']);

// 处理下拉菜单点击
const handleDropdownClick = (category, type) => {
    emit('dropdown-click', { category, type });

    // 根据类型导航到对应页面
    switch (category) {
        case 'solutions':
            navigateTo(`/solutions/${type}`);
            break;
        case 'products':
            navigateTo(`/products/${type}`);
            break;
    }
};

// 处理导航点击
const handleNavClick = (nav) => {
    emit('nav-click', nav);
};
</script>

<style lang="scss" scoped>
// 首页导航栏
.home-nav {
    background-color: #ffffff;
    padding: 19px 30px;
    border-radius: 12px;

    // 导航列表
    &__list {
        display: flex;
        align-items: center;
        gap: 40px;
    }

    // 导航项
    &__item {
        display: flex;
        align-items: center;
        color: #4b5563;
        text-decoration: none;
        transition: color 0.3s ease;
        position: relative;
        cursor: pointer;

        &--active {
            color: #3b82f6;
        }

        // 带标签的导航项
        &--with-badge {
            position: relative;

            &::before {
                content: attr(data-badge);
                position: absolute;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                background-color: #ff7d00;
                color: white;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 6px;
                white-space: nowrap;
                text-align: center;
                min-width: 80px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
            }

            &::after {
                content: '';
                position: absolute;
                top: -14px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-top: 8px solid #ff7d00;
                z-index: 10;
            }
        }
    }

    // 导航图标
    &__icon {
        width: 20px;
        height: 20px;
    }

    // 导航文字
    &__text {
        margin-left: 5px;
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
    }

    // 导航箭头
    &__arrow {
        margin-left: 4px;
        font-size: 14px;
    }
}
</style>
