<template>
    <section class="quality-merchants">
        <div class="quality-merchants__container">
            <!-- 标题区域 -->
            <div class="quality-merchants__header">
                <h2 class="section-title">优质商家</h2>
                <p class="section-subtitle">实力商家 行业优选</p>
            </div>

            <!-- 分类筛选 -->
            <div class="quality-merchants__filters">
                <button v-for="category in categories" :key="category.id" :class="['filter-btn', { 'filter-btn--active': activeCategory === category.id }]" @click="setActiveCategory(category.id)">
                    {{ category.name }}
                </button>
            </div>

            <!-- 商家列表 -->
            <div class="quality-merchants__list">
                <div v-for="merchant in filteredMerchants" :key="merchant.id" class="merchant-card">
                    <!-- 商家信息头部 -->
                    <div class="merchant-card__header">
                        <div class="merchant-info">
                            <h3 class="merchant-name">
                                <span class="merchant-name__icon merchant-name__icon--left">
                                    <span></span>
                                    <span></span>
                                </span>
                                {{ merchant.name }}
                                <span class="merchant-name__icon merchant-name__icon--right">
                                    <span></span>
                                    <span></span>
                                </span>
                            </h3>
                            <div class="merchant-tags">
                                <span v-for="tag in merchant.tags" :key="tag" class="merchant-tag" :class="{ verified: tag === '已通过真实性核验' }">
                                    {{ tag }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- 商家图片展示区域 -->
                    <div class="merchant-card__images">
                        <div class="images-container">
                            <div v-for="(image, index) in merchant.images" :key="index" class="image-item">
                                <img :src="image.url || '/images/home/<USER>'" :alt="image.alt || `${merchant.name}图片${index + 1}`" @error="handleImageError" />
                            </div>
                        </div>
                    </div>

                    <!-- 商家操作区域 -->
                    <div class="merchant-card__footer">
                        <NuxtLink :to="`/merchant/${merchant.id}`" class="view-merchant-btn"> 查看商家 > </NuxtLink>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup>
// 响应式数据
const activeCategory = ref('all');

// 分类数据
const categories = [
    { id: 'all', name: '全部' },
    { id: 'power', name: '配电系统' },
    { id: 'air', name: '空调系统' },
    { id: 'ups', name: 'UPS系统' },
    { id: 'eps', name: 'EPS系统' },
    { id: 'energy', name: '机房能源电气系统' },
];

// 商家数据（示例数据）
const merchants = [
    {
        id: '1',
        name: '常州瑞物科技有限公司',
        description: '专业的工业电气设备供应商',
        images: [
            { url: '/images/home/<USER>', alt: '公司外观1' },
            { url: '/images/home/<USER>', alt: '公司外观2' },
        ],
        tags: ['厂商', '已通过真实性核验'],
        category: 'power',
    },
    {
        id: '2',
        name: '常州瑞物科技有限公司',
        description: '专业的工业电气设备供应商',
        images: [{ url: '/images/home/<USER>', alt: '公司全景' }],
        tags: ['厂商', '已通过真实性核验'],
        category: 'air',
    },
    {
        id: '3',
        name: '常州瑞物科技有限公司',
        description: '专业的工业电气设备供应商',
        images: [
            { url: '/images/home/<USER>', alt: '生产车间1' },
            { url: '/images/home/<USER>', alt: '生产车间2' },
            { url: '/images/home/<USER>', alt: '生产车间3' },
        ],
        tags: ['厂商', '已通过真实性核验'],
        category: 'ups',
    },
];

// 计算属性：过滤后的商家列表
const filteredMerchants = computed(() => {
    if (activeCategory.value === 'all') {
        return merchants;
    }
    return merchants.filter((merchant) => merchant.category === activeCategory.value);
});

// 方法：设置活跃分类
const setActiveCategory = (categoryId) => {
    activeCategory.value = categoryId;
};

// 方法：处理图片加载错误
const handleImageError = (event) => {
    const img = event.target;
    img.src = '/images/home/<USER>';
};
</script>

<style lang="scss" scoped>
.quality-merchants {
    padding: 20px 20px 29px;
    border-radius: 12px;
    background-image: url('/assets/images/home/<USER>');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        gap: 10px;

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #1d2129;
        }

        .section-subtitle {
            font-size: 16px;
            color: #1d2129;
        }
    }

    &__filters {
        display: flex;
        gap: 6px;
        margin-bottom: 20px;

        .filter-btn {
            padding: 6px 37px;
            border: 1px solid transparent;
            border-radius: 6px;
            background-color: #ffffff;
            color: #4e5969;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                border-color: rgba(7, 131, 255, 0.5);
                color: #0783ff;
            }

            &--active {
                border-color: rgba(7, 131, 255, 0.5);
                color: #0783ff;
            }
        }
    }

    &__list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
}

// 商家卡片样式
.merchant-card {
    border-radius: 12px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    &__header {
        margin-top: 10px;
        margin-bottom: 7px;

        .merchant-info {
            .merchant-name {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-weight: 700;
                color: #1d2129;
                margin-bottom: 7px;
                text-align: center;

                &__icon {
                    display: flex;
                    align-items: center;

                    span {
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: #12c5ff;
                    }

                    &--left {
                        margin-right: 10px;
                    }

                    &--right {
                        margin-left: 10px;
                    }
                }
            }

            .merchant-tags {
                display: flex;
                justify-content: center;
                gap: 10px;
                flex-wrap: wrap;

                .merchant-tag {
                    padding: 4px 8px;
                    background-color: #e6f2ff;
                    color: #0783ff;
                    font-size: 13px;
                    border-radius: 6px;
                    border: 1px solid rgba(7, 131, 255, 0.5);

                    &.verified {
                        background-color: #e8ffea;
                        color: #00b42a;
                        border-color: #00b42a;
                    }
                }
            }
        }
    }

    &__images {
        padding: 0 20px;
        margin-bottom: 10px;

        .images-container {
            display: flex;
            gap: 10px;
            border-radius: 8px;
            overflow: hidden;

            .image-item {
                flex: 1;
                height: 80px;
                border-radius: 8px;
                overflow: hidden;
                background-color: #f5f5f5;

                img {
                    width: 100%;
                    height: 100%;
                    display: block;
                    transition: transform 0.3s ease;

                    &:hover {
                        transform: scale(1.05);
                    }
                }
            }
        }
    }

    &__footer {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 38px;
        background-color: #e6f2ff;

        .view-merchant-btn {
            color: #0783ff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                color: #0099cc;
                background-color: #f0f9ff;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .quality-merchants {
        padding: 40px 0;

        &__container {
            padding: 0 16px;
        }

        &__header {
            margin-bottom: 30px;

            .section-title {
                font-size: 24px;
            }

            .section-subtitle {
                font-size: 14px;
            }
        }

        &__filters {
            gap: 12px;
            margin-bottom: 30px;

            .filter-btn {
                padding: 6px 16px;
                font-size: 13px;
            }
        }

        &__list {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }

    .merchant-card {
        padding: 16px;

        &__images {
            .images-container {
                &--single .image-item {
                    height: 160px;
                }

                &--double .image-item {
                    height: 120px;
                }

                &--triple .image-item {
                    height: 100px;
                }
            }
        }
    }
}
</style>
