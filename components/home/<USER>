<template>
    <div class="design-process">
        <h2 class="design-process__title">方案设计流程</h2>

        <div class="design-process__steps">
            <div v-for="(step, index) in steps" :key="step.id" class="design-process__step">
                <!-- 步骤图标 -->
                <div class="design-process__step-icon">
                    <img src="~/assets/images/home/<USER>" :alt="step.title" />
                </div>

                <!-- 步骤标题 -->
                <div class="design-process__step-title">{{ step.title }}</div>

                <!-- 连接箭头 -->
                <div v-if="index < steps.length - 1" class="design-process__arrow">
                    <svg width="60" height="20" viewBox="0 0 60 20" fill="none">
                        <path d="M0 10H50M45 5L50 10L45 15" stroke="#4A90E2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 设计流程步骤
const steps = reactive([
    {
        id: 1,
        title: '需求方案',
    },
    {
        id: 2,
        title: '生成方案',
    },
    {
        id: 3,
        title: '深化设计',
    },
    {
        id: 4,
        title: '上门安装',
    },
    {
        id: 5,
        title: '售后服务',
    },
]);
</script>

<style lang="scss" scoped>
.design-process {
    &__title {
        font-size: 20px;
        font-weight: 700;
        color: #3d3d3d;
        margin-bottom: 20px;
    }

    &__steps {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        max-width: 1000px;
        margin: 0 auto;
    }

    &__step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }

    &__step-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        transition: transform 0.3s ease;

        &:hover {
            transform: translateY(-3px);
        }

        img {
            width: 40px;
            height: 40px;
            filter: brightness(0) invert(1);
        }
    }

    &__step-title {
        font-size: 16px;
        font-weight: 600;
        color: #1d2129;
        white-space: nowrap;
    }

    &__arrow {
        position: absolute;
        right: -40px;
        top: 35px;
        z-index: 1;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .design-process {
        &__steps {
            flex-direction: column;
            gap: 30px;
        }

        &__arrow {
            display: none;
        }
    }
}
</style>
