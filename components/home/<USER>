<template>
    <div class="application-scenes">
        <h2 class="application-scenes__title">应用场景</h2>

        <div class="application-scenes__grid">
            <div v-for="scene in scenes" :key="scene.id" class="scene-card" @click="handleSceneClick(scene)">
                <!-- 场景图片 -->
                <div class="scene-card__image">
                    <img :src="scene.image" :alt="scene.title" />
                    <div class="scene-card__overlay">
                        <h3 class="scene-card__title">{{ scene.title }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 应用场景数据
const scenes = reactive([
    {
        id: 1,
        title: '工厂车间',
        image: '/images/home/<USER>',
    },
    {
        id: 2,
        title: '银行机房',
        image: '/images/home/<USER>',
    },
    {
        id: 3,
        title: '数据中心',
        image: '/images/home/<USER>',
    },
    {
        id: 4,
        title: '医院设施',
        image: '/images/home/<USER>',
    },
]);

// 事件处理
const emit = defineEmits(['scene-click']);

const handleSceneClick = (scene) => {
    emit('scene-click', scene);
};
</script>

<style lang="scss" scoped>
.application-scenes {
    margin-top: 20px;
    &__title {
        font-size: 20px;
        font-weight: 700;
        color: #3d3d3d;
        margin-bottom: 20px;
    }

    &__grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }
}

.scene-card {
    cursor: pointer;
    border-radius: 12px;
    overflow: hidden;
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &__image {
        position: relative;
        height: 141px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
    }

    &__overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover &__overlay {
        opacity: 1;
    }

    &:hover &__image img {
        transform: scale(1.1);
    }

    &__title {
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        margin: 0;
    }
}

// 响应式设计
@media (max-width: 1024px) {
    .application-scenes__grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 640px) {
    .application-scenes__grid {
        grid-template-columns: 1fr;
    }
}
</style>
