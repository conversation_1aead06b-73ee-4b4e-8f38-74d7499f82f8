<template>
    <div class="featured-products">
        <!-- 标题区域 -->
        <div class="featured-products__header">
            <h3 class="featured-products__title">
                <i class="featured-products__title-icon fas fa-star"></i>
                {{ title }}
            </h3>
            <div class="featured-products__tabs">
                <button v-for="tab in tabs" :key="tab.id" class="featured-products__tab" @click="switchTab(tab.id)">
                    {{ tab.name }}
                </button>
            </div>
        </div>

        <!-- 产品网格 -->
        <div class="featured-products__grid">
            <div v-for="product in currentProducts" :key="product.id" class="product-card" @click="handleProductClick(product)">
                <!-- 产品图片 -->
                <div class="product-card__image">
                    <img src="~/assets/images/home/<USER>" :alt="product.name" class="product-card__img" />
                </div>

                <!-- 产品信息 -->
                <div class="product-card__content">
                    <h4 class="product-card__title">
                        {{ product.name }}
                    </h4>
                    <p class="product-card__subtitle">
                        {{ product.subtitle }}
                    </p>

                    <!-- 产品标签 -->
                    <div v-if="product.tags" class="product-card__tags">
                        <span v-for="tag in product.tags" :key="tag" :class="getTagClass(tag)" class="product-card__tag">
                            {{ tag }}
                        </span>
                    </div>

                    <!-- 价格信息 -->
                    <div v-if="product.price" class="product-card__price-container">
                        <div class="product-card__price">
                            <span class="product-card__price-symbol">¥</span>
                            <span class="product-card__price-integer">{{ Math.floor(product.price) }}</span>
                            <span class="product-card__price-decimal">.{{ (product.price % 1).toFixed(2).slice(2) }}</span>
                        </div>
                        <button class="product-card__buy-btn">立即订购</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// Props
const props = defineProps({
    title: {
        type: String,
        default: '商城单品优选',
    },
});

// 响应式数据
const activeTab = ref('solutions');

// 标签页配置
const tabs = reactive([
    { id: 'solutions', name: '商务方案/产品' },
    { id: 'pricing', name: '价格透明高效' },
]);

// 产品数据
const products = reactive({
    solutions: [
        {
            id: 1,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 2,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 3,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 4,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 5,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
    ],
    pricing: [
        {
            id: 6,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 7,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 8,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 9,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
        {
            id: 10,
            name: '瑞物 RW-G12款体电池储物',
            subtitle: 'RW-G12款体电池',
            image: null,
            tags: [],
            price: 5666.0,
            category: 'battery',
            manufacturer: '瑞物云科技',
            specs: {
                model: 'RW-G12',
                type: '体电池储物',
                capacity: '12Ah',
            },
        },
    ],
});

// 计算属性
const currentProducts = computed(() => {
    return products[activeTab.value] || [];
});

// 方法
const switchTab = (tabId) => {
    activeTab.value = tabId;
};

// 事件处理
const emit = defineEmits(['product-click']);

const handleProductClick = (product) => {
    emit('product-click', product);
};

// 获取标签样式类
const getTagClass = (tag) => {
    const tagClasses = {
        热销: 'product-card__tag--hot',
        新品: 'product-card__tag--new',
        推荐: 'product-card__tag--recommended',
    };
    return tagClasses[tag] || 'product-card__tag--default';
};
</script>

<style lang="scss" scoped>
// 特色产品组件
.featured-products {
    padding: 20px;
    background-color: #ffffff;
    border-radius: 12px;

    // 头部区域
    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    // 标题
    &__title {
        color: #1d2129;
        display: flex;
        align-items: center;
        margin-right: 15px;
        font-size: 20px;
        font-weight: 700;
    }

    // 标签页
    &__tabs {
        display: flex;
        gap: 16px;
    }

    &__tab {
        font-size: 14px;
        color: #86909c;
        background-color: transparent;
    }

    // 产品网格
    &__grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
    }
}

// 产品卡片
.product-card {
    border-radius: 8px;
    padding: 4px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;

    &:hover {
        // 悬浮时的高亮效果
        border: 2px solid #f53f3f;
        background-color: #ffffff;
    }

    // 产品图片
    &__image {
        height: 200px;
        background-color: #f7f8fc;
        border-radius: 12px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        transition: transform 0.3s ease;
    }

    &__img {
        width: 100%;
        height: 100%;
    }

    &__placeholder {
        color: #6b7280;
        font-size: 14px;
    }

    // 产品内容
    &__content {
        margin-bottom: 12px;
    }

    &__title {
        font-size: 14px;
        font-weight: 500;
        color: #1d2129;
        transition: color 0.3s ease;
        text-align: left;
    }

    &__subtitle {
        font-size: 14px;
        color: #1d2129;
        margin-bottom: 8px;
        text-align: left;
    }

    // 标签
    &__tags {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
    }

    &__tag {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 9999px;

        &--hot {
            background-color: #fef2f2;
            color: #dc2626;
        }

        &--new {
            background-color: #eff6ff;
            color: #2563eb;
        }

        &--recommended {
            background-color: #f0fdf4;
            color: #16a34a;
        }

        &--default {
            background-color: #f3f4f6;
            color: #4b5563;
        }
    }

    // 价格容器
    &__price-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #f53f3f;
        background-color: #f53f3f;
        border-radius: 4px;
        overflow: hidden;
    }

    // 价格
    &__price {
        font-size: 14px;
        font-weight: 700;
        color: #fff;
        display: flex;
        align-items: baseline;
        padding: 4px 10px;

        &-symbol {
            margin-right: 2px;
            font-size: 14px;
            line-height: 1;
        }

        &-integer {
            font-size: 18px;
            font-weight: 800;
            line-height: 1;
        }

        &-decimal {
            font-size: 14px;
            font-weight: 700;
            line-height: 1;
        }
    }

    // 抢购按钮
    &__buy-btn {
        width: 68px;
        height: 26px;
        background-color: #fff;
        color: #f53f3f;
        border: none;
        font-size: 12px;
        font-weight: 700;
        cursor: pointer;
        position: relative;

        // 向右的三角形伪元素
        &::after {
            content: '';
            position: absolute;
            left: -14px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-right: 14px solid #fff;
            border-top: 14px solid transparent;
            border-bottom: 14px solid transparent;
        }
    }
}
</style>
