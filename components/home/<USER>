<template>
    <div class="product-section">
        <!-- 左侧介绍区域 -->
        <div class="product-section__intro" :style="{ backgroundImage: `url(${introImage})` }">
            <div class="product-section__intro-content" :style="{ background: introBackgroundColor }">
                <h3 class="product-section__title">
                    {{ title }}
                </h3>
                <p class="product-section__subtitle">{{ subtitle }}</p>
                <div class="product-section__divider"></div>
                <p class="product-section__description">{{ description }}</p>
                <button class="product-section__more-btn" @click="handleMoreClick">
                    {{ moreButtonText }}
                    <el-icon>
                        <ArrowRight />
                    </el-icon>
                </button>
            </div>
        </div>

        <!-- 右侧产品区域 -->
        <div class="product-section__products">
            <!-- 标签页 -->
            <div class="product-section__tabs">
                <button v-for="tab in tabs" :key="tab.id" :class="['product-section__tab', { 'product-section__tab--active': activeTab === tab.id }]" @click="switchTab(tab.id)">
                    {{ tab.name }}
                </button>
            </div>

            <!-- 产品网格 -->
            <div class="product-section__grid">
                <CommonProductItem v-for="product in currentProducts" :key="product.id" :product="product" :default-image="defaultImage" @click="handleProductClick" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ArrowRight } from '@element-plus/icons-vue';
// Props
const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    subtitle: {
        type: String,
        default: '',
    },
    description: {
        type: String,
        default: '',
    },
    moreButtonText: {
        type: String,
        default: '查看更多',
    },
    introImage: {
        type: String,
        default: '',
    },
    introBackgroundColor: {
        type: String,
        default: '',
    },
    tabs: {
        type: Array,
        required: true,
    },
    products: {
        type: Object,
        required: true,
    },
    defaultImage: {
        type: String,
        default: '/images/home/<USER>',
    },
});

// 响应式数据
const activeTab = ref(props.tabs[0]?.id || '');

// 计算属性
const currentProducts = computed(() => {
    return props.products[activeTab.value] || [];
});

// 方法
const switchTab = (tabId) => {
    activeTab.value = tabId;
};

// 事件处理
const emit = defineEmits(['product-click', 'more-click']);

const handleProductClick = (product) => {
    emit('product-click', product);
};

const handleMoreClick = () => {
    emit('more-click', { section: props.title, tab: activeTab.value });
};
</script>

<style lang="scss" scoped>
.product-section {
    display: flex;
    gap: 20px;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    min-height: 500px;

    // 左侧介绍区域
    &__intro {
        width: 216px;
        color: white;

        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        background-size: cover;
        background-repeat: no-repeat;
    }

    &__intro-content {
        padding: 30px 20px;
    }

    &__title {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
        line-height: 1.2;
    }

    &__subtitle {
        font-size: 16px;
        margin-bottom: 15px;
    }

    &__divider {
        width: 48px;
        height: 2px;
        background-color: #fff;
        margin: 55px 0 24px;
    }

    &__description {
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 36px;
        color: #fff;
    }

    &__more-btn {
        display: flex;
        align-items: center;
        background-color: transparent;
        color: white;
        border: 1px solid white;
        padding: 6px 8px 6px 16px;
        border-radius: 6px;
        font-size: 16px;
        cursor: pointer;
    }

    &__intro-image {
        margin-top: 20px;
        text-align: center;

        img {
            max-width: 100%;
            height: auto;
            opacity: 0.8;
        }
    }

    // 右侧产品区域
    &__products {
        flex: 1;
    }

    &__tabs {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    &__tab {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 106px;
        height: 36px;
        background-color: #f7f8fc;
        font-size: 16px;
        color: #86909c;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
            color: #4a90e2;
            border-color: #4a90e2;
        }

        &--active {
            color: #0783ff;
            background-color: #e6f2ff;
            border: 1px solid #0783ff;
            font-weight: 500;
        }
    }

    &__grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
}

// 产品项样式已移至 ProductItem 组件
</style>
