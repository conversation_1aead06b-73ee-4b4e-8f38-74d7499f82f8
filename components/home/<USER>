<template>
    <div class="category-sidebar">
        <!-- 标题栏 -->
        <div class="category-sidebar__header" :class="{ 'category-sidebar__header--expanded': !isExpanded }">
            <span class="category-sidebar__title">瑞物在线商城</span>
            <el-icon class="category-sidebar__arrow" @click="handleToggleExpand">
                <ArrowDown v-if="isExpanded" />
                <ArrowUp v-else />
            </el-icon>
        </div>

        <!-- 分类内容 -->
        <div v-show="isExpanded" class="category-sidebar__content">
            <!-- 成套方案 -->
            <div class="category-sidebar__section">
                <div class="category-sidebar__section-header" @click="handleSectionClick('solutions')">
                    <img src="~/assets/images/home/<USER>" alt="成套方案图标" class="category-sidebar__section-icon" />
                    <span class="category-sidebar__section-title">成套方案</span>
                    <el-icon class="category-sidebar__section-arrow"><ArrowRight /></el-icon>
                </div>
                <div class="category-sidebar__items">
                    <NuxtLink v-for="solution in solutionsList" :key="solution.id" :to="solution.url" class="category-sidebar__item" @click="handleItemClick('solution', solution)">
                        {{ solution.name }}
                    </NuxtLink>
                </div>
            </div>

            <!-- 商城单品 -->
            <div class="category-sidebar__section">
                <div class="category-sidebar__section-header" @click="handleSectionClick('products')">
                    <img src="~/assets/images/home/<USER>" alt="商城单品图标" class="category-sidebar__section-icon" />
                    <span class="category-sidebar__section-title">商城单品</span>
                    <el-icon class="category-sidebar__section-arrow"><ArrowRight /></el-icon>
                </div>
                <div class="category-sidebar__items">
                    <NuxtLink v-for="product in productsList" :key="product.id" :to="product.url" class="category-sidebar__item" @click="handleItemClick('product', product)">
                        {{ product.name }}
                    </NuxtLink>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 导入Element Plus图标
import { ArrowDown, ArrowUp, ArrowRight } from '@element-plus/icons-vue';

// 定义事件
const emit = defineEmits(['category-click', 'section-click', 'item-click', 'sidebar-toggle']);

// 侧边栏展开/收缩状态管理
const isExpanded = ref(true);

// 组件挂载
onMounted(() => {
    // 组件初始化完成
});

// 成套方案数据
const solutionsList = reactive([
    { id: 1, name: '机房能源配电系统', url: '/solutions/power-system', code: 'power-system' },
    { id: 2, name: '配电系统', url: '/solutions/distribution', code: 'distribution' },
    { id: 3, name: 'UPS系统', url: '/solutions/ups', code: 'ups' },
    { id: 4, name: '空调系统', url: '/solutions/hvac', code: 'hvac' },
    { id: 5, name: 'EPS系统', url: '/solutions/eps', code: 'eps' },
]);

// 商城单品数据
const productsList = reactive([
    { id: 1, name: 'UPS电源', url: '/products/ups-power', code: 'ups-power' },
    { id: 2, name: '蓄电池', url: '/products/battery', code: 'battery' },
    { id: 3, name: 'EPS电池', url: '/products/eps-battery', code: 'eps-battery' },
    { id: 4, name: '电池线', url: '/products/battery-cable', code: 'battery-cable' },
    { id: 5, name: '电池柜', url: '/products/battery-cabinet', code: 'battery-cabinet' },
]);

// 事件处理函数
const handleToggleExpand = () => {
    isExpanded.value = !isExpanded.value;
    emit('sidebar-toggle', isExpanded.value);
};

const handleFixed = (fixed) => {
    isExpanded.value = fixed;
    emit('sidebar-toggle', fixed);
};

const handleSectionClick = (section) => {
    console.log('分类点击:', section);
    emit('section-click', section);
};

const handleItemClick = (type, item) => {
    console.log('项目点击:', type, item);
    emit('item-click', { type, item });

    // 同时发送给父组件的category-click事件（保持向后兼容）
    emit('category-click', item);
};

defineExpose({
    handleFixed,
});
</script>

<style lang="scss" scoped>
// 分类侧边栏
.category-sidebar {
    width: 256px;
    background-color: #ffffff;
    border-radius: 12px;
    overflow: visible; // 改为visible以显示悬浮内容
    position: relative; // 为内容区域的绝对定位提供参考

    // 标题栏
    &__header {
        height: 60px;
        padding: 0 16px;
        background: #2291ff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 12px 12px 0 0;
        position: relative;
        z-index: 1000;

        &--expanded {
            border-radius: 12px;
        }
    }

    &__title {
        font-size: 18px;
        font-weight: 500;
        color: #ffffff;
    }

    &__arrow {
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.1);
        }
    }

    // 内容区域
    &__content {
        padding: 13px 12px;
        transition:
            opacity 0.3s ease,
            max-height 0.3s ease;
        overflow: hidden;

        // 悬浮模式：内容区域绝对定位，不影响布局
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #ffffff;
        border-radius: 0 0 12px 12px;
        z-index: 999;
    }

    // 分类区块
    &__section {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    // 分类标题
    &__section-header {
        display: flex;
        align-items: center;
        margin-bottom: 7px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            color: #3b82f6;
        }
    }

    &__section-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
    }

    &__section-title {
        font-size: 16px;
        font-weight: 500;
        color: #1f2937;
        flex: 1;
    }

    &__section-arrow {
        margin-left: 4px;
        color: #9ca3af;
        font-size: 14px;
        transition: color 0.3s ease;
    }

    // 项目列表
    &__items {
        display: flex;
        flex-direction: column;
    }

    // 单个项目
    &__item {
        display: block;
        padding: 3px 0;
        font-size: 16px;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.3s ease;

        &:hover {
            color: #3b82f6;
            transform: translateX(2px);
        }

        &:focus {
            outline: none;
            color: #3b82f6;
        }
    }
}
</style>
