<template>
    <div class="info-sidebar">
        <!-- 用户信息区域 -->
        <div class="info-sidebar__card">
            <!-- 问候语 -->
            <div class="info-sidebar__greeting">
                <img src="~/assets/images/home/<USER>" alt="云图标" class="info-sidebar__greeting-icon" />
                <div class="info-sidebar__greeting-text">
                    <div class="info-sidebar__greeting-title">Hi 你好！</div>
                    <div class="info-sidebar__greeting-subtitle">欢迎来到瑞物商城！</div>
                </div>
            </div>

            <!-- 用户类型按钮 -->
            <div class="info-sidebar__user-types">
                <button class="info-sidebar__user-type-btn">我是用户</button>
                <button class="info-sidebar__user-type-btn merchant">我是商家</button>
            </div>

            <!-- 功能图标 -->
            <div class="info-sidebar__functions">
                <div class="info-sidebar__function-item">
                    <img src="~/assets/images/home/<USER>" alt="个人中心" class="info-sidebar__function-icon" />
                    <span class="info-sidebar__function-label">个人中心</span>
                </div>
                <div class="info-sidebar__function-item">
                    <img src="~/assets/images/home/<USER>" alt="我的咨询" class="info-sidebar__function-icon" />
                    <span class="info-sidebar__function-label">我的咨询</span>
                </div>
                <div class="info-sidebar__function-item">
                    <img src="~/assets/images/home/<USER>" alt="我的收藏" class="info-sidebar__function-icon" />
                    <span class="info-sidebar__function-label">我的收藏</span>
                </div>
            </div>

            <!-- 联系客服 -->
            <div class="info-sidebar__contact">
                <div class="info-sidebar__contact-header">
                    <div class="info-sidebar__contact-info">
                        <span class="info-sidebar__contact-title">联系客服</span>
                        <span class="info-sidebar__contact-status">24小时在线</span>
                    </div>
                    <div class="info-sidebar__contact-phone">
                        <span class="info-sidebar__phone-number">400-6611-323</span>
                    </div>
                </div>

                <div class="info-sidebar__phone-icon">
                    <img width="32px" height="32px" src="~/assets/images/home/<USER>" alt="" />
                </div>
            </div>
        </div>

        <!-- 新闻区域 -->
        <div class="info-sidebar__card">
            <!-- 标签页切换 -->
            <div class="info-sidebar__news-tabs">
                <div class="info-sidebar__news-tab" :class="{ 'info-sidebar__news-tab--active': activeTab === 'news' }" @click="activeTab = 'news'">实时新闻</div>
                <div class="info-sidebar__news-tab" :class="{ 'info-sidebar__news-tab--active': activeTab === 'knowledge' }" @click="activeTab = 'knowledge'">瑞物知识</div>
                <div v-if="activeTab === 'knowledge'" class="info-sidebar__news-more" @click="goToKnowledgeList">
                    <el-icon><ArrowRight /></el-icon>
                </div>
            </div>

            <!-- 新闻列表 -->
            <div class="info-sidebar__news-list">
                <div v-for="item in currentNewsList" :key="item.id" class="info-sidebar__news-item">
                    <div class="info-sidebar__news-content">{{ item.title }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 导入图标
import { ArrowRight } from '@element-plus/icons-vue';

// 当前激活的标签页
const activeTab = ref('news');

// 实时新闻数据
const newsList = reactive([
    {
        id: 1,
        title: '政府行业UPS电源解决方案（下）',
    },
    {
        id: 2,
        title: '农村电网建设中的机房方案',
    },
    {
        id: 3,
        title: '数据中心综合管理、可用性和效率三大核心...',
    },
    {
        id: 4,
        title: '农村电网建设中的机房方案',
    },
]);

// 瑞物知识数据
const knowledgeList = reactive([
    {
        id: 1,
        title: 'UPS电源系统维护保养指南',
    },
    {
        id: 2,
        title: '数据中心机房建设标准规范',
    },
    {
        id: 3,
        title: '电力设备选型与配置要点',
    },
    {
        id: 4,
        title: '智能配电系统解决方案',
    },
]);

// 计算当前显示的新闻列表
const currentNewsList = computed(() => {
    return activeTab.value === 'news' ? newsList : knowledgeList;
});

// 跳转到瑞物知识列表页
const goToKnowledgeList = () => {
    // 使用Nuxt的navigateTo进行路由跳转
    navigateTo('/knowledge');
};
</script>

<style lang="scss" scoped>
// 信息侧边栏
.info-sidebar {
    width: 266px;
    min-height: 400px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;

    // 卡片容器
    &__card {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 16px;
    }

    // 问候语区域
    &__greeting {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;

        &-icon {
            margin-right: 8px;
        }
        &-title {
            font-size: 14px;
            color: #1d2129;
            margin-bottom: 2px;
        }

        &-subtitle {
            font-size: 14px;
            color: #1d2129;
        }
    }

    // 用户类型按钮区域
    &__user-types {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 12px;
    }

    &__user-type-btn {
        width: 80px;
        border: 1px solid #12c5ff;
        color: #12c5ff;
        background-color: transparent;
        font-size: 14px;
        padding: 6px 0;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        box-sizing: content-box;

        &.merchant {
            color: #0783ff;
            border-color: #0783ff;
        }
    }

    // 功能图标区域
    &__functions {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
    }

    &__function-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
            transform: translateY(-2px);
        }
    }

    &__function-icon {
        margin-bottom: 4px;
    }

    &__function-label {
        font-size: 13px;
        color: #1d2129;
    }

    // 联系客服区域
    &__contact {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: linear-gradient(180deg, #e9f4ff 8%, rgba(231, 243, 255, 0) 100%);
        border-radius: 12px;

        &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: column;
            margin-bottom: 8px;
        }

        &-title {
            margin-right: 4px;
            font-size: 12px;
            color: #4e5969;
        }

        &-status {
            font-size: 10px;
            color: #f59e0b;
        }

        &-phone {
            display: flex;
            align-items: center;
        }
    }

    &__phone-number {
        font-size: 14px;
        font-weight: 700;
        color: #0783ff;
    }

    &__phone-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    // 新闻标签页区域
    &__card {
        padding: 9px 12px;
        background: linear-gradient(180deg, #e6f4ff 4%, rgba(242, 243, 245, 0) 100%), #ffffff;
    }
    &__news-tabs {
        display: flex;
        align-items: center;
        position: relative;
    }

    &__news-tab {
        font-size: 14px;
        line-height: 1.5;
        color: #86909c;
        cursor: pointer;
        margin-right: 12px;
        transition: color 0.2s ease;
        position: relative;

        &:hover {
            color: #6b7280;
        }

        &--active {
            color: #0783ff;
            font-weight: 500;
        }
    }

    &__news-more {
        margin-left: auto;
        color: #9ca3af;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
            font-size: 16px;
        }
    }

    &__news-list {
        display: flex;
        flex-direction: column;
    }

    &__news-item {
        cursor: pointer;
        border-radius: 6px;
        padding: 1px 0;
        transition: background-color 0.2s ease;
    }

    &__news-content {
        font-size: 12px;
        color: #1f2937;
        line-height: 1.5;

        // 单行文本超出显示省略号
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
            color: #0783ff;
        }
    }
}
</style>
