<template>
    <HomeProductSection
        title="成套配置化方案"
        subtitle="支持DIY方案"
        description="推动生产全要素与数据智能融合，重塑生产价值链，让生产看得见"
        :show-brand="false"
        more-button-text="查看更多"
        intro-image="/images/home/<USER>"
        introBackgroundColor="linear-gradient( 180deg, #28B2FF 0%, rgba(183,195,206,0) 98%);"
        :tabs="tabs"
        :products="products"
        @product-click="handleProductClick"
        @more-click="handleMoreClick"
    />
</template>

<script setup>
// ProductSection 组件会自动导入为 HomeProductSection;

// 标签页配置
const tabs = reactive([
    { id: 'distribution', name: '配电柜' },
    { id: 'aircon', name: '空调系统' },
    { id: 'lightning', name: '防雷接地系统' },
    { id: 'ups', name: 'UPS系统' },
    { id: 'eps', name: 'EPS系统' },
]);

// 产品数据
const products = reactive({
    distribution: [
        {
            id: 1,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
            isHot: true,
        },
        {
            id: 2,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
        {
            id: 3,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
        {
            id: 4,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
        {
            id: 5,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
        {
            id: 6,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
        {
            id: 7,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
        {
            id: 8,
            name: '三进单出20KVA UPS 整体解决方案',
            subtitle: '整体解决方案 A级 含蓄电池20*',
            price: 5666.0,
        },
    ],
    aircon: [
        // 空调系统产品数据...
    ],
    lightning: [
        // 防雷接地系统产品数据...
    ],
    ups: [
        // UPS系统产品数据...
    ],
    eps: [
        // EPS系统产品数据...
    ],
});

// 事件处理
const emit = defineEmits(['product-click', 'more-click']);

const handleProductClick = (product) => {
    emit('product-click', product);
};

const handleMoreClick = (data) => {
    emit('more-click', data);
    navigateTo(`/solutions?section=${data.section}&tab=${data.tab}`);
};
</script>
