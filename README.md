# 瑞物云商城前端项目

基于 Nuxt.js 3 + Vue 3 + TypeScript 构建的现代化电商前端项目。

## 🚀 技术栈

- **框架**: Nuxt.js 3 + Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **CSS框架**: UnoCSS
- **状态管理**: Pinia
- **工具库**: VueUse
- **图片优化**: Nuxt Image

## 📦 项目结构

```
rwy-web-mall/
├── .env.development       # 开发环境配置
├── .env.staging          # 测试环境配置
├── .env.production       # 生产环境配置
├── assets/               # 静态资源
├── components/           # 组件
├── composables/          # 组合式函数
├── layouts/              # 布局模板
├── pages/                # 页面
├── stores/               # Pinia状态管理
├── types/                # TypeScript类型定义
└── utils/                # 工具函数
```

## 🛠️ 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

## 📥 安装依赖

```bash
npm install
```

## 🌍 多环境配置

项目支持三个环境的配置：

### 开发环境 (Development)

- **配置文件**: `.env.development`
- **特点**: 本地开发、调试工具、详细日志
- **启动**: `npm run dev`

### 测试环境 (Staging)

- **配置文件**: `.env.staging`
- **特点**: 接近生产配置、错误追踪
- **启动**: `npm run dev:staging`

### 生产环境 (Production)

- **配置文件**: `.env.production`
- **特点**: 完全优化、安全配置
- **构建**: `npm run build:prod`

## 🚀 开发服务器

启动开发服务器 (http://localhost:3000):

```bash
# 开发环境
npm run dev

# 测试环境
npm run dev:staging
```

## 🏗️ 构建和部署

### 构建命令

```bash
# 开发环境构建
npm run build:dev

# 测试环境构建
npm run build:staging

# 生产环境构建
npm run build:prod

# 静态生成
npm run generate
npm run generate:staging
```

### 预览命令

```bash
# 预览测试环境
npm run preview:staging

# 预览生产环境
npm run preview:prod
```

## 📝 可用脚本

```bash
# 开发
npm run dev                    # 开发环境
npm run dev:staging           # 测试环境

# 构建
npm run build:dev             # 开发构建
npm run build:staging         # 测试构建
npm run build:prod            # 生产构建

# 预览
npm run preview:staging       # 测试预览
npm run preview:prod          # 生产预览

# 工具
npm run lint                  # 代码检查
npm run lint:fix              # 修复代码问题
npm run type-check            # 类型检查
npm run clean                 # 清理缓存
```

## 🔧 环境配置说明

### 查看环境配置

```bash
# 查看开发环境配置
cat .env.development

# 查看测试环境配置
cat .env.staging

# 查看生产环境配置
cat .env.production
```

### 主要环境变量

- `NODE_ENV`: Node.js环境标识
- `NUXT_PUBLIC_ENV`: 前端环境标识
- `NUXT_PUBLIC_APP_NAME`: 应用名称
- `NUXT_PUBLIC_BASE_URL`: 前端基础URL
- `NUXT_PUBLIC_API_BASE_URL`: API服务器地址
- `NUXT_DEVTOOLS`: 是否启用开发工具
- `NUXT_PUBLIC_DEBUG`: 是否启用调试模式

## 🎯 功能特性

- ✅ 服务端渲染 (SSR)
- ✅ 静态站点生成 (SSG)
- ✅ 响应式设计
- ✅ SEO优化
- ✅ 图片优化
- ✅ 代码分割
- ✅ 热重载
- ✅ TypeScript支持
- ✅ 状态管理
- ✅ 路由中间件
- ✅ 组件自动导入
- ✅ 多环境配置

## 📄 许可证

本项目采用 MIT 许可证

---

© 2024 瑞物云. 保留所有权利.
