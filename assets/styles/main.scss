/* 瑞物云商城全局样式 */

/* 全局重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

body {
    background-color: #f5f5f5;
    font-size: 14px;
}

/* 链接样式 */
a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #2563eb;
}

/* 按钮基础样式 */
button {
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.3s ease;
}

/* 输入框基础样式 */
input,
textarea,
select {
    outline: none;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

input:focus,
textarea:focus,
select:focus {
    border-color: #3b82f6;
}

/* 工具类 */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.flex {
    display: flex;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start {
    display: flex;
    align-items: center;
}

.hidden {
    display: none;
}

.block {
    display: block;
}

/* 响应式工具类 */
@media (max-width: 768px) {
    .hidden-mobile {
        display: none;
    }
}

@media (min-width: 769px) {
    .hidden-desktop {
        display: none;
    }
}
