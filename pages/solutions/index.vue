<template>
    <CommonBaseListPage
        :filter-config="filterConfig"
        :items="listStore.items"
        :total="listStore.total"
        :loading="listStore.loading"
        :sort-options="sortOptions"
        :empty-text="emptyText"
        @filter-change="handleFilterChange"
        @sort-change="handleSortChange"
        @view-change="handleViewChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @item-click="handleItemClick"
    />
</template>

<script setup>
// 页面元数据
useHead({
    title: '方案列表 - 瑞物云商城',
    meta: [
        { name: 'description', content: '瑞物云方案列表页面，提供各种电气设备解决方案' },
        { name: 'keywords', content: '电气方案,UPS方案,配电方案,数据中心方案,瑞物云' },
    ],
});

// Store
const listStore = useListStore();
const filterStore = useFilterStore();
const route = useRoute();
const router = useRouter();

// 页面配置
const emptyText = '暂无方案数据';

// 排序选项
const sortOptions = [
    { label: '综合排序', value: 'default' },
    { label: '最新发布', value: 'created_desc' },
    { label: '最受欢迎', value: 'popular_desc' },
    { label: '价格从低到高', value: 'price_asc' },
    { label: '价格从高到低', value: 'price_desc' },
];

// 筛选器配置
const filterConfig = {
    categories: [
        { label: '数据中心', value: 'datacenter' },
        { label: '安防监控', value: 'security' },
        { label: '交通运输', value: 'transport' },
        { label: '应急通信', value: 'emergency' },
        { label: '石油石化', value: 'petrochemical' },
        { label: '医疗设备', value: 'medical' },
        { label: '工业制造', value: 'manufacturing' },
        { label: '金融机构', value: 'finance' },
    ],
    priceRange: true,
    priceRanges: [
        { label: '1万以下', min: 0, max: 10000 },
        { label: '1-5万', min: 10000, max: 50000 },
        { label: '5-10万', min: 50000, max: 100000 },
        { label: '10-50万', min: 100000, max: 500000 },
        { label: '50万以上', min: 500000, max: null },
    ],
    brands: [
        { label: '华为', value: 'huawei' },
        { label: '施耐德', value: 'schneider' },
        { label: '艾默生', value: 'emerson' },
        { label: 'ABB', value: 'abb' },
        { label: '西门子', value: 'siemens' },
        { label: '台达', value: 'delta' },
        { label: '山特', value: 'santak' },
        { label: '科华', value: 'kelong' },
    ],
    customFilters: [
        {
            key: 'application_scene',
            label: '应用场景',
            type: 'checkbox',
            options: [
                { label: '机房建设', value: 'server_room' },
                { label: '工厂车间', value: 'factory' },
                { label: '办公楼宇', value: 'office' },
                { label: '医院设施', value: 'hospital' },
                { label: '学校教育', value: 'education' },
                { label: '商业中心', value: 'commercial' },
            ],
        },
        {
            key: 'power_range',
            label: '功率范围',
            type: 'radio',
            options: [
                { label: '小功率(≤10kVA)', value: 'small' },
                { label: '中功率(10-100kVA)', value: 'medium' },
                { label: '大功率(100-500kVA)', value: 'large' },
                { label: '超大功率(≥500kVA)', value: 'extra_large' },
            ],
        },
        {
            key: 'solution_complexity',
            label: '方案复杂度',
            type: 'range',
            min: 1,
            max: 10,
            step: 1,
        },
    ],
};

// 模拟API函数
const fetchSolutions = async (params) => {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 模拟数据
    const mockData = Array.from({ length: 20 }, (_, index) => ({
        id: index + 1,
        name: `数据中心UPS解决方案 ${index + 1}`,
        description: '专业的数据中心UPS电源解决方案，提供高可靠性的电力保障',
        price: Math.floor(Math.random() * 500000) + 10000,
        image: '~/assets/images/home/<USER>',
        tags: [
            { text: '包邮', type: 'free-shipping' },
            { text: '现货', type: 'in-stock' },
        ],
        category: 'datacenter',
        brand: 'huawei',
        sales: `已售${Math.floor(Math.random() * 1000) + 100}+`,
    }));

    return {
        success: true,
        data: {
            items: mockData,
            total: 156,
        },
    };
};

// 事件处理
const handleFilterChange = (filters) => {
    listStore.setFilters(filters);
    updateUrlParams();
    loadData();
};

const handleSortChange = (sort) => {
    listStore.setCurrentSort(sort);
    updateUrlParams();
    loadData();
};

const handleViewChange = (view) => {
    listStore.setCurrentView(view);
    updateUrlParams();
};

const handlePageChange = (page) => {
    listStore.setCurrentPage(page);
    updateUrlParams();
    loadData();
};

const handleSizeChange = (size) => {
    listStore.setPageSize(size);
    updateUrlParams();
    loadData();
};

const handleItemClick = (item) => {
    router.push(`/solutions/${item.id}`);
};

// URL参数同步
const updateUrlParams = () => {
    const params = listStore.getCurrentParams();
    const query = {
        page: params.page > 1 ? params.page : undefined,
        pageSize: params.pageSize !== 20 ? params.pageSize : undefined,
        sort: params.sort !== 'default' ? params.sort : undefined,
        view: params.view !== 'grid' ? params.view : undefined,
    };

    // 添加筛选参数
    if (params.filters.categories.length > 0) {
        query.categories = params.filters.categories.join(',');
    }
    if (params.filters.brands.length > 0) {
        query.brands = params.filters.brands.join(',');
    }
    if (params.filters.priceRange.min !== null || params.filters.priceRange.max !== null) {
        query.priceMin = params.filters.priceRange.min;
        query.priceMax = params.filters.priceRange.max;
    }

    router.replace({ query });
};

// 从URL恢复参数
const restoreFromUrl = () => {
    const query = route.query;
    const params = {
        page: query.page ? parseInt(query.page) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize) : 20,
        sort: query.sort || 'default',
        view: query.view || 'grid',
        filters: {
            categories: query.categories ? query.categories.split(',') : [],
            brands: query.brands ? query.brands.split(',') : [],
            priceRange: {
                min: query.priceMin ? parseInt(query.priceMin) : null,
                max: query.priceMax ? parseInt(query.priceMax) : null,
            },
            custom: {},
        },
    };

    listStore.restoreFromParams(params);
};

// 加载数据
const loadData = () => {
    listStore.fetchList(fetchSolutions);
};

// 页面初始化
onMounted(() => {
    // 设置搜索类型为方案
    listStore.setSearchType('solution');

    // 设置筛选器配置
    filterStore.setFilterConfig('solution', filterConfig);
    filterStore.setActiveConfig('solution');

    // 从URL恢复参数
    restoreFromUrl();

    // 加载数据
    loadData();
});

// 页面卸载时重置状态
onUnmounted(() => {
    listStore.resetState();
});
</script>
