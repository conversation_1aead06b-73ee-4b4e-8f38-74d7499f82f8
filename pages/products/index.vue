<template>
    <BaseListPage
        :page-title="pageTitle"
        :page-subtitle="pageSubtitle"
        :breadcrumb-items="breadcrumbItems"
        :filter-config="filterConfig"
        :items="listStore.items"
        :total="listStore.total"
        :loading="listStore.loading"
        :sort-options="sortOptions"
        :empty-text="emptyText"
        @filter-change="handleFilterChange"
        @sort-change="handleSortChange"
        @view-change="handleViewChange"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @item-click="handleItemClick"
    />
</template>

<script setup>
// 页面元数据
useHead({
    title: '商品列表 - 瑞物云商城',
    meta: [
        { name: 'description', content: '瑞物云商品列表页面，提供各种电气设备产品' },
        { name: 'keywords', content: 'UPS电源,EPS电源,蓄电池,配电柜,机房设备,瑞物云' }
    ]
})

// Store
const listStore = useListStore()
const filterStore = useFilterStore()
const route = useRoute()
const router = useRouter()

// 页面配置
const pageTitle = '商品列表'
const pageSubtitle = '优质的电气设备产品，源头直供，品质保证'
const emptyText = '暂无商品数据'

// 面包屑导航
const breadcrumbItems = [
    { label: '首页', path: '/' },
    { label: '商品列表', path: '/products' }
]

// 排序选项
const sortOptions = [
    { label: '综合排序', value: 'default' },
    { label: '销量优先', value: 'sales_desc' },
    { label: '价格从低到高', value: 'price_asc' },
    { label: '价格从高到低', value: 'price_desc' },
    { label: '最新上架', value: 'created_desc' },
    { label: '好评优先', value: 'rating_desc' }
]

// 筛选器配置
const filterConfig = {
    categories: [
        { label: 'UPS电源', value: 'ups' },
        { label: 'EPS电源', value: 'eps' },
        { label: '蓄电池', value: 'battery' },
        { label: '配电柜', value: 'distribution' },
        { label: '机房设备', value: 'server_room' },
        { label: '监控设备', value: 'monitoring' },
        { label: '防雷设备', value: 'lightning' },
        { label: '空调设备', value: 'hvac' }
    ],
    priceRange: true,
    priceRanges: [
        { label: '100元以下', min: 0, max: 100 },
        { label: '100-500元', min: 100, max: 500 },
        { label: '500-1000元', min: 500, max: 1000 },
        { label: '1000-5000元', min: 1000, max: 5000 },
        { label: '5000-10000元', min: 5000, max: 10000 },
        { label: '10000元以上', min: 10000, max: null }
    ],
    brands: [
        { label: '山特', value: 'santak' },
        { label: '科华', value: 'kelong' },
        { label: '易事特', value: 'east' },
        { label: '台达', value: 'delta' },
        { label: '艾默生', value: 'emerson' },
        { label: '施耐德', value: 'schneider' },
        { label: 'APC', value: 'apc' },
        { label: '华为', value: 'huawei' },
        { label: '中兴', value: 'zte' },
        { label: '英威腾', value: 'invt' }
    ],
    customFilters: [
        {
            key: 'power_rating',
            label: '功率等级',
            type: 'checkbox',
            options: [
                { label: '1kVA以下', value: 'under_1kva' },
                { label: '1-3kVA', value: '1_3kva' },
                { label: '3-6kVA', value: '3_6kva' },
                { label: '6-10kVA', value: '6_10kva' },
                { label: '10-20kVA', value: '10_20kva' },
                { label: '20kVA以上', value: 'over_20kva' }
            ]
        },
        {
            key: 'voltage_level',
            label: '电压等级',
            type: 'radio',
            options: [
                { label: '220V', value: '220v' },
                { label: '380V', value: '380v' },
                { label: '双电压', value: 'dual' },
                { label: '其他', value: 'other' }
            ]
        },
        {
            key: 'certification',
            label: '认证标准',
            type: 'checkbox',
            options: [
                { label: 'CCC认证', value: 'ccc' },
                { label: 'CE认证', value: 'ce' },
                { label: 'ISO9001', value: 'iso9001' },
                { label: 'ISO14001', value: 'iso14001' },
                { label: 'ROHS', value: 'rohs' }
            ]
        },
        {
            key: 'warranty_period',
            label: '质保期限',
            type: 'range',
            min: 1,
            max: 5,
            step: 1
        }
    ]
}

// 模拟API函数
const fetchProducts = async (params) => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 600))
    
    // 模拟数据
    const mockData = Array.from({ length: 20 }, (_, index) => ({
        id: index + 1,
        name: `台达UPS不间断电源 ${index + 1}kVA`,
        description: '高效可靠的UPS不间断电源，适用于各种应用场景',
        price: Math.floor(Math.random() * 10000) + 500,
        image: '~/assets/images/home/<USER>',
        tags: [
            { text: '包邮', type: 'free-shipping' },
            { text: '现货', type: 'in-stock' },
            ...(Math.random() > 0.5 ? [{ text: '热销', type: 'hot' }] : [])
        ],
        category: 'ups',
        brand: 'delta',
        sales: `已售${Math.floor(Math.random() * 500) + 50}+`,
        rating: (Math.random() * 2 + 3).toFixed(1)
    }))
    
    return {
        success: true,
        data: {
            items: mockData,
            total: 328
        }
    }
}

// 事件处理
const handleFilterChange = (filters) => {
    listStore.setFilters(filters)
    updateUrlParams()
    loadData()
}

const handleSortChange = (sort) => {
    listStore.setCurrentSort(sort)
    updateUrlParams()
    loadData()
}

const handleViewChange = (view) => {
    listStore.setCurrentView(view)
    updateUrlParams()
}

const handlePageChange = (page) => {
    listStore.setCurrentPage(page)
    updateUrlParams()
    loadData()
}

const handleSizeChange = (size) => {
    listStore.setPageSize(size)
    updateUrlParams()
    loadData()
}

const handleItemClick = (item) => {
    router.push(`/products/${item.id}`)
}

// URL参数同步
const updateUrlParams = () => {
    const params = listStore.getCurrentParams()
    const query = {
        page: params.page > 1 ? params.page : undefined,
        pageSize: params.pageSize !== 20 ? params.pageSize : undefined,
        sort: params.sort !== 'default' ? params.sort : undefined,
        view: params.view !== 'grid' ? params.view : undefined
    }
    
    // 添加筛选参数
    if (params.filters.categories.length > 0) {
        query.categories = params.filters.categories.join(',')
    }
    if (params.filters.brands.length > 0) {
        query.brands = params.filters.brands.join(',')
    }
    if (params.filters.priceRange.min !== null || params.filters.priceRange.max !== null) {
        query.priceMin = params.filters.priceRange.min
        query.priceMax = params.filters.priceRange.max
    }
    
    router.replace({ query })
}

// 从URL恢复参数
const restoreFromUrl = () => {
    const query = route.query
    const params = {
        page: query.page ? parseInt(query.page) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize) : 20,
        sort: query.sort || 'default',
        view: query.view || 'grid',
        filters: {
            categories: query.categories ? query.categories.split(',') : [],
            brands: query.brands ? query.brands.split(',') : [],
            priceRange: {
                min: query.priceMin ? parseInt(query.priceMin) : null,
                max: query.priceMax ? parseInt(query.priceMax) : null
            },
            custom: {}
        }
    }
    
    listStore.restoreFromParams(params)
}

// 加载数据
const loadData = () => {
    listStore.fetchList(fetchProducts)
}

// 页面初始化
onMounted(() => {
    // 设置搜索类型为商品
    listStore.setSearchType('product')
    
    // 设置筛选器配置
    filterStore.setFilterConfig('product', filterConfig)
    filterStore.setActiveConfig('product')
    
    // 从URL恢复参数
    restoreFromUrl()
    
    // 加载数据
    loadData()
})

// 页面卸载时重置状态
onUnmounted(() => {
    listStore.resetState()
})
</script>
