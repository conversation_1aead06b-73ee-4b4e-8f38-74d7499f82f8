<template>
    <div class="homepage">
        <div class="homepage__container">
            <!-- 主要内容区域 -->
            <div class="homepage__content" :class="{ 'category-collapsed': !categoryExpanded }">
                <!-- 左侧轮播图区域 -->
                <div class="homepage__banner">
                    <HomeHeroBanner @action-click="handleBannerAction" />
                </div>

                <!-- 右侧用户信息栏 -->
                <div class="homepage__info">
                    <HomeInfoSidebar />
                </div>
            </div>

            <!-- 统计数据展示 -->
            <div class="homepage__stats">
                <HomeStatsSection @stat-click="handleStatClick" />
            </div>

            <!-- 商城重点产品 -->
            <div class="homepage__products">
                <HomeFeaturedProducts @product-click="handleProductClick" @view-more="handleViewMore" />
            </div>

            <!-- 成套配置化方案 -->
            <div class="homepage__configuration">
                <HomeConfigurationSection @product-click="handleProductClick" @more-click="handleMoreClick" />
            </div>

            <div class="homepage__process-scenes">
                <!-- 方案设计流程 -->
                <HomeDesignProcess />
                <!-- 应用场景 -->
                <HomeApplicationScenes @scene-click="handleSceneClick" />
            </div>

            <!-- 产品直供 -->
            <div class="homepage__direct-supply">
                <HomeDirectSupplySection @product-click="handleProductClick" @more-click="handleMoreClick" />
            </div>

            <!-- 优质商家 -->
            <div class="homepage__quality-merchants">
                <HomeQualityMerchants />
            </div>
        </div>

        <!-- 宣传图 -->
        <div class="homepage__ad"><img src="~/assets/images/home/<USER>" alt="宣传图" /></div>

        <!-- 滚动到顶部 -->
        <el-backtop :right="34" :bottom="193">
            <img width="41px" height="41px" src="~/assets/images/home/<USER>" alt="" />
        </el-backtop>
    </div>
</template>

<script setup>
// 移除了useElementBounding导入，不再需要

// 页面元数据
useHead({
    title: '首页 - 瑞物云商城',
    meta: [
        { name: 'description', content: '瑞物云电气设备商城首页，专业的电气设备采购平台' },
        { name: 'keywords', content: 'UPS电源,EPS电源,配电柜,蓄电池,机房设备,瑞物云' },
    ],
});

// 移除了fixedNavControl注入，现在由布局层统一管理

// 从布局注入分类导航状态
const categoryNavControl = inject('categoryNavControl');
const categoryExpanded = categoryNavControl?.categoryExpanded || ref(true);

// 移除了之前基于特定元素的滚动监听逻辑
// 现在由布局层统一管理固定导航的显示

// 页面数据
const pageData = reactive({
    loading: false,
    error: null,
    lastUpdate: new Date(),
});

// 首页特有的事件处理函数

const handleBannerAction = ({ action, slide }) => {
    console.log('轮播图操作:', action, slide);

    switch (action.type) {
        case 'inquiry':
            navigateTo('/inquiry');
            break;
        case 'products':
            navigateTo('/products');
            break;
        case 'details':
            navigateTo('/products/ups');
            break;
        case 'quote':
            navigateTo('/quote');
            break;
        case 'solution':
            navigateTo('/solutions');
            break;
        case 'cases':
            navigateTo('/cases');
            break;
    }
};

const handleStatClick = (stat) => {
    console.log('统计数据点击:', stat);
};

const handleProductClick = (product) => {
    console.log('产品点击:', product);
    navigateTo(`/products/${product.category}/${product.id}`);
};

const handleViewMore = ({ tab }) => {
    console.log('查看更多:', tab);
    navigateTo(`/products?tab=${tab}`);
};

const handleMoreClick = (data) => {
    console.log('查看更多:', data);
    navigateTo(`/products?section=${data.section}&tab=${data.tab}`);
};

const handleSceneClick = (scene) => {
    console.log('应用场景点击:', scene);
    navigateTo(`/scenes/${scene.id}`);
};

// 页面加载时的数据获取
const loadPageData = async () => {
    try {
        pageData.loading = true;
        await new Promise((resolve) => setTimeout(resolve, 500));
        pageData.lastUpdate = new Date();
    } catch (error) {
        pageData.error = error.message;
        console.error('页面数据加载失败:', error);
    } finally {
        pageData.loading = false;
    }
};

// 生命周期
onMounted(() => {
    loadPageData();
});
</script>

<style lang="scss" scoped>
// 首页主容器
.homepage {
    background-color: #f5f5f5;
    min-height: calc(100vh - 200px);

    // 容器
    &__container {
        width: 1200px;
        margin: 0 auto;
    }

    // 主布局
    &__layout {
        display: flex;
        gap: 16px;
    }

    // 左侧边栏
    &__sidebar {
        flex-shrink: 0;
    }

    // 主要内容区域
    &__main {
        flex: 1;
    }

    // 导航包装器
    &__nav-wrapper {
        position: relative;
        margin-bottom: 16px;
    }

    // 内容区域
    &__content {
        display: flex;
        gap: 16px;
        margin-left: 272px; // 为左侧分类导航留出空间（256px + 16px间距）
        transition: margin-left 0.3s ease;

        // 当分类导航收缩时的样式
        &.category-collapsed {
            margin-left: 0; // 移除左边距，让内容占据全宽
        }
    }

    // 轮播图区域
    &__banner {
        flex: 1;
        transition: all 0.3s ease;
    }

    // 信息栏区域
    &__info {
        flex-shrink: 0;
        width: 266px;
    }

    // 统计数据区域
    &__stats {
        margin-top: 16px;
    }

    // 产品展示区域
    &__products {
        margin-top: 16px;
    }

    // 成套配置化方案区域
    &__configuration {
        margin-top: 16px;
    }

    // 应用场景区域
    &__process-scenes {
        margin-top: 16px;
        background-color: #fff;
        border-radius: 12px;
        padding: 20px;
    }

    // 产品直供区域
    &__direct-supply {
        margin-top: 16px;
    }

    // 优质商家区域
    &__quality-merchants {
        margin-top: 16px;
    }

    // 宣传图区域
    &__ad {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        text-align: center;
        background-color: #0783ff;
        font-size: 0;
    }

    // 滚动到顶部样式

    :deep(.el-backtop) {
        background-color: transparent;
        border: none;
        box-shadow: none;
    }
}
</style>
