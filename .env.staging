# 瑞物云商城 - 测试环境配置

# 环境标识
NODE_ENV=production
NUXT_PUBLIC_ENV=staging

# 应用信息
NUXT_PUBLIC_APP_NAME=瑞物云商城 (测试)
NUXT_PUBLIC_APP_VERSION=1.0.0-staging
NUXT_PUBLIC_APP_DESCRIPTION=瑞物云电气设备商城测试环境

# 前端服务器配置
NUXT_PUBLIC_BASE_URL=https://staging.rwyun.cn
NUXT_HOST=0.0.0.0
NUXT_PORT=3000

# API配置
NUXT_PUBLIC_API_BASE_URL=https://staging-api.rwyun.cn/api
NUXT_PUBLIC_API_TIMEOUT=15000

# 开发工具配置
NUXT_DEVTOOLS=false
NUXT_SSR=true
NUXT_SOURCEMAP=true

# 调试配置
NUXT_PUBLIC_DEBUG=true
NUXT_PUBLIC_LOG_LEVEL=info

# 性能配置
NUXT_PUBLIC_ENABLE_ANALYTICS=true
NUXT_PUBLIC_ENABLE_ERROR_TRACKING=true

# 功能开关
NUXT_PUBLIC_ENABLE_PWA=true
NUXT_PUBLIC_ENABLE_I18N=false
NUXT_PUBLIC_ENABLE_MOCK_DATA=false

# CDN配置
NUXT_PUBLIC_CDN_URL=https://cdn-staging.rwyun.cn
NUXT_PUBLIC_STATIC_URL=https://static-staging.rwyun.cn

# 第三方服务配置（测试环境）
NUXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA-STAGING-ID
NUXT_PUBLIC_SENTRY_DSN=https://staging-sentry-dsn

# 缓存配置
NUXT_PUBLIC_CACHE_ENABLED=true
NUXT_PUBLIC_CACHE_TTL=300

# SEO配置
NUXT_PUBLIC_ROBOTS_ENABLED=false
NUXT_PUBLIC_SITEMAP_ENABLED=true
