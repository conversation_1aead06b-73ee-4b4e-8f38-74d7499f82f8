<template>
    <div class="app">
        <!-- 顶部区域 -->
        <LayoutAppHeader @search="handleSearch" @inquiry="handleInquiry" @nav-click="handleTopNavClick" @login="handleLogin" />

        <!-- 页面内容区域 -->
        <main class="app__main">
            <div class="app__content">
                <slot />
            </div>
        </main>

        <!-- 底部 -->
        <LayoutAppFooter />
    </div>
</template>

<script setup>
// 页面元数据
useHead({
    titleTemplate: '%s - 瑞物云商城',
    meta: [{ name: 'description', content: '瑞物云电气设备商城，专业的电气设备采购平台' }],
});

// 移除固定导航状态管理，由AppHeader自己处理

// 分类导航状态管理
const route = useRoute();
const categoryExpanded = computed(() => {
    // 只有首页显示分类导航
    return route.path === '/';
});

// 提供分类导航控制函数给子组件
const setCategoryExpanded = (expanded) => {
    categoryExpanded.value = expanded;
};

provide('categoryNavControl', {
    setCategoryExpanded,
    categoryExpanded: readonly(categoryExpanded),
});

// 全局事件处理
const handleSearch = (searchData) => {
    console.log('全局搜索:', searchData);
    navigateTo(`/search?type=${searchData.type}&keyword=${encodeURIComponent(searchData.keyword)}`);
};

const handleInquiry = () => {
    console.log('全局询价');
    navigateTo('/inquiry');
};

const handleTopNavClick = (nav) => {
    console.log('顶部导航点击:', nav);
    navigateTo(nav.url);
};

const handleLogin = (link) => {
    console.log('登录点击:', link);
    navigateTo('/login');
};
</script>

<style lang="scss" scoped>
// 应用主布局
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;

    // 主内容区域
    &__main {
        flex: 1;
        width: 100%;
    }

    // 内容容器
    &__content {
        max-width: 1920px;
        margin: 0 auto;
        width: 100%;
        min-height: calc(100vh - 200px);
        padding: 0 spacing(5);

        // 响应式设计
        @media (max-width: 1920px) {
            max-width: 100%;
        }

        @media (max-width: 1200px) {
            padding: 0 spacing(4);
        }

        @media (max-width: 768px) {
            padding: 0 spacing(3);
        }
    }

    // 移除固定导航样式，由AppHeader自己处理
}
</style>
