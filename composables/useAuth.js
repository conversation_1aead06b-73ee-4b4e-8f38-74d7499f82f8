// 用户认证状态管理

export const useAuth = () => {
  // 用户状态
  const user = useState('auth.user', () => null)
  const token = useState('auth.token', () => null)
  const isLoggedIn = computed(() => !!user.value && !!token.value)

  /**
   * 登录
   */
  const login = async (credentials) => {
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟用户数据
      const mockUser = {
        id: 1,
        username: credentials.username,
        email: credentials.email || '<EMAIL>',
        role: 'user'
      }
      const mockToken = 'mock-jwt-token-' + Date.now()
      
      user.value = mockUser
      token.value = mockToken
      
      // 保存到localStorage
      if (process.client) {
        localStorage.setItem('auth_token', mockToken)
        localStorage.setItem('auth_user', JSON.stringify(mockUser))
      }
      
      return { success: true, message: '登录成功' }
    } catch (error) {
      console.error('登录错误:', error)
      return { success: false, message: '登录失败' }
    }
  }

  /**
   * 登出
   */
  const logout = async () => {
    // 清除状态
    user.value = null
    token.value = null

    // 清除localStorage
    if (process.client) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
    }

    // 重定向到首页
    await navigateTo('/')
  }

  /**
   * 初始化认证状态（从localStorage恢复）
   */
  const initAuth = () => {
    if (process.client) {
      const savedToken = localStorage.getItem('auth_token')
      const savedUser = localStorage.getItem('auth_user')
      
      if (savedToken && savedUser) {
        try {
          token.value = savedToken
          user.value = JSON.parse(savedUser)
        } catch (error) {
          console.error('恢复认证状态失败:', error)
          localStorage.removeItem('auth_token')
          localStorage.removeItem('auth_user')
        }
      }
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoggedIn,
    
    // 方法
    login,
    logout,
    initAuth
  }
}
