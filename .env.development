# 瑞物云商城 - 开发环境配置

# 环境标识
NODE_ENV=development
NUXT_PUBLIC_ENV=development

# 应用信息
NUXT_PUBLIC_APP_NAME=瑞物云商城 (开发)
NUXT_PUBLIC_APP_VERSION=1.0.0-dev
NUXT_PUBLIC_APP_DESCRIPTION=瑞物云电气设备商城开发环境

# 前端服务器配置
NUXT_PUBLIC_BASE_URL=http://localhost:3000
NUXT_HOST=localhost
NUXT_PORT=3000

# API配置
NUXT_PUBLIC_API_BASE_URL=http://localhost:8080/api
NUXT_PUBLIC_API_TIMEOUT=30000

# 开发工具配置
NUXT_DEVTOOLS=true
NUXT_SSR=true
NUXT_SOURCEMAP=true

# 调试配置
NUXT_PUBLIC_DEBUG=true
NUXT_PUBLIC_LOG_LEVEL=debug

# 性能配置
NUXT_PUBLIC_ENABLE_ANALYTICS=false
NUXT_PUBLIC_ENABLE_ERROR_TRACKING=false

# 功能开关
NUXT_PUBLIC_ENABLE_PWA=false
NUXT_PUBLIC_ENABLE_I18N=false
NUXT_PUBLIC_ENABLE_MOCK_DATA=true

# CDN配置
NUXT_PUBLIC_CDN_URL=
NUXT_PUBLIC_STATIC_URL=/

# 第三方服务配置（开发环境）
NUXT_PUBLIC_GOOGLE_ANALYTICS_ID=
NUXT_PUBLIC_SENTRY_DSN=

# 缓存配置
NUXT_PUBLIC_CACHE_ENABLED=false
NUXT_PUBLIC_CACHE_TTL=0
