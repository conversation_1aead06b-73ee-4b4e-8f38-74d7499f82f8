import { defineStore } from 'pinia'

export const useListStore = defineStore('list', () => {
    // 状态
    const items = ref([])
    const total = ref(0)
    const loading = ref(false)
    const error = ref(null)
    
    // 分页状态
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
    
    // 排序状态
    const currentSort = ref('default')
    const currentView = ref('grid')
    
    // 筛选状态
    const filters = ref({
        categories: [],
        brands: [],
        priceRange: {
            min: null,
            max: null
        },
        custom: {}
    })
    
    // 搜索状态
    const searchKeyword = ref('')
    const searchType = ref('product') // product | solution
    
    // Actions
    const setItems = (newItems) => {
        items.value = newItems
    }
    
    const setTotal = (newTotal) => {
        total.value = newTotal
    }
    
    const setLoading = (isLoading) => {
        loading.value = isLoading
    }
    
    const setError = (newError) => {
        error.value = newError
    }
    
    const setCurrentPage = (page) => {
        currentPage.value = page
    }
    
    const setPageSize = (size) => {
        pageSize.value = size
        currentPage.value = 1 // 重置到第一页
    }
    
    const setCurrentSort = (sort) => {
        currentSort.value = sort
        currentPage.value = 1 // 重置到第一页
    }
    
    const setCurrentView = (view) => {
        currentView.value = view
    }
    
    const setFilters = (newFilters) => {
        filters.value = { ...filters.value, ...newFilters }
        currentPage.value = 1 // 重置到第一页
    }
    
    const clearFilters = () => {
        filters.value = {
            categories: [],
            brands: [],
            priceRange: {
                min: null,
                max: null
            },
            custom: {}
        }
        currentPage.value = 1
    }
    
    const setSearchKeyword = (keyword) => {
        searchKeyword.value = keyword
        currentPage.value = 1 // 重置到第一页
    }
    
    const setSearchType = (type) => {
        searchType.value = type
        currentPage.value = 1 // 重置到第一页
    }
    
    // 获取列表数据的通用方法
    const fetchList = async (apiFunction, params = {}) => {
        try {
            setLoading(true)
            setError(null)
            
            const requestParams = {
                page: currentPage.value,
                pageSize: pageSize.value,
                sort: currentSort.value,
                filters: filters.value,
                keyword: searchKeyword.value,
                type: searchType.value,
                ...params
            }
            
            const response = await apiFunction(requestParams)
            
            if (response.success) {
                setItems(response.data.items || [])
                setTotal(response.data.total || 0)
            } else {
                throw new Error(response.message || '获取数据失败')
            }
        } catch (err) {
            console.error('获取列表数据失败:', err)
            setError(err.message || '获取数据失败')
            setItems([])
            setTotal(0)
        } finally {
            setLoading(false)
        }
    }
    
    // 重置所有状态
    const resetState = () => {
        items.value = []
        total.value = 0
        loading.value = false
        error.value = null
        currentPage.value = 1
        pageSize.value = 20
        currentSort.value = 'default'
        currentView.value = 'grid'
        clearFilters()
        searchKeyword.value = ''
        searchType.value = 'product'
    }
    
    // 获取当前查询参数
    const getCurrentParams = () => {
        return {
            page: currentPage.value,
            pageSize: pageSize.value,
            sort: currentSort.value,
            view: currentView.value,
            filters: filters.value,
            keyword: searchKeyword.value,
            type: searchType.value
        }
    }
    
    // 从URL参数恢复状态
    const restoreFromParams = (params) => {
        if (params.page) currentPage.value = parseInt(params.page)
        if (params.pageSize) pageSize.value = parseInt(params.pageSize)
        if (params.sort) currentSort.value = params.sort
        if (params.view) currentView.value = params.view
        if (params.keyword) searchKeyword.value = params.keyword
        if (params.type) searchType.value = params.type
        
        // 恢复筛选条件
        if (params.filters) {
            try {
                const parsedFilters = typeof params.filters === 'string' 
                    ? JSON.parse(params.filters) 
                    : params.filters
                filters.value = { ...filters.value, ...parsedFilters }
            } catch (err) {
                console.warn('解析筛选参数失败:', err)
            }
        }
    }
    
    return {
        // 状态
        items: readonly(items),
        total: readonly(total),
        loading: readonly(loading),
        error: readonly(error),
        currentPage: readonly(currentPage),
        pageSize: readonly(pageSize),
        totalPages,
        currentSort: readonly(currentSort),
        currentView: readonly(currentView),
        filters: readonly(filters),
        searchKeyword: readonly(searchKeyword),
        searchType: readonly(searchType),
        
        // Actions
        setItems,
        setTotal,
        setLoading,
        setError,
        setCurrentPage,
        setPageSize,
        setCurrentSort,
        setCurrentView,
        setFilters,
        clearFilters,
        setSearchKeyword,
        setSearchType,
        fetchList,
        resetState,
        getCurrentParams,
        restoreFromParams
    }
})
