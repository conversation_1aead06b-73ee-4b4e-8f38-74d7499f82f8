import { defineStore } from 'pinia'

export const useFilterStore = defineStore('filter', () => {
    // 筛选器配置缓存
    const filterConfigs = ref({})
    
    // 当前激活的筛选器配置
    const activeConfig = ref({})
    
    // 筛选器数据缓存（如分类列表、品牌列表等）
    const filterData = ref({
        categories: [],
        brands: [],
        customOptions: {}
    })
    
    // 加载状态
    const loading = ref(false)
    const error = ref(null)
    
    // Actions
    const setFilterConfig = (type, config) => {
        filterConfigs.value[type] = config
        if (!activeConfig.value.type || activeConfig.value.type === type) {
            activeConfig.value = { type, ...config }
        }
    }
    
    const setActiveConfig = (type) => {
        if (filterConfigs.value[type]) {
            activeConfig.value = { type, ...filterConfigs.value[type] }
        }
    }
    
    const setFilterData = (data) => {
        filterData.value = { ...filterData.value, ...data }
    }
    
    const setLoading = (isLoading) => {
        loading.value = isLoading
    }
    
    const setError = (newError) => {
        error.value = newError
    }
    
    // 获取筛选器数据
    const fetchFilterData = async (type, apiFunction) => {
        try {
            setLoading(true)
            setError(null)
            
            const response = await apiFunction(type)
            
            if (response.success) {
                setFilterData(response.data)
            } else {
                throw new Error(response.message || '获取筛选数据失败')
            }
        } catch (err) {
            console.error('获取筛选数据失败:', err)
            setError(err.message || '获取筛选数据失败')
        } finally {
            setLoading(false)
        }
    }
    
    // 验证筛选值
    const validateFilters = (filters, config) => {
        const validatedFilters = { ...filters }
        
        // 验证分类
        if (config.categories && validatedFilters.categories) {
            const validCategories = config.categories.map(cat => cat.value || cat)
            validatedFilters.categories = validatedFilters.categories.filter(cat => 
                validCategories.includes(cat)
            )
        }
        
        // 验证品牌
        if (config.brands && validatedFilters.brands) {
            const validBrands = config.brands.map(brand => brand.value || brand)
            validatedFilters.brands = validatedFilters.brands.filter(brand => 
                validBrands.includes(brand)
            )
        }
        
        // 验证价格范围
        if (validatedFilters.priceRange) {
            const { min, max } = validatedFilters.priceRange
            if (min !== null && max !== null && min > max) {
                validatedFilters.priceRange = { min: max, max: min }
            }
        }
        
        // 验证自定义筛选器
        if (config.customFilters && validatedFilters.custom) {
            config.customFilters.forEach(filter => {
                const value = validatedFilters.custom[filter.key]
                if (value !== undefined) {
                    if (filter.type === 'radio' || filter.type === 'checkbox') {
                        const validOptions = filter.options.map(opt => opt.value)
                        if (Array.isArray(value)) {
                            validatedFilters.custom[filter.key] = value.filter(v => 
                                validOptions.includes(v)
                            )
                        } else if (!validOptions.includes(value)) {
                            validatedFilters.custom[filter.key] = filter.type === 'checkbox' ? [] : ''
                        }
                    }
                }
            })
        }
        
        return validatedFilters
    }
    
    // 格式化筛选器用于显示
    const formatFiltersForDisplay = (filters, config) => {
        const formatted = []
        
        // 分类
        if (filters.categories && filters.categories.length > 0) {
            const categoryLabels = filters.categories.map(cat => {
                const categoryConfig = config.categories?.find(c => (c.value || c) === cat)
                return categoryConfig?.label || cat
            })
            formatted.push({
                type: 'categories',
                label: '分类',
                values: categoryLabels,
                count: categoryLabels.length
            })
        }
        
        // 品牌
        if (filters.brands && filters.brands.length > 0) {
            const brandLabels = filters.brands.map(brand => {
                const brandConfig = config.brands?.find(b => (b.value || b) === brand)
                return brandConfig?.label || brand
            })
            formatted.push({
                type: 'brands',
                label: '品牌',
                values: brandLabels,
                count: brandLabels.length
            })
        }
        
        // 价格范围
        if (filters.priceRange && (filters.priceRange.min !== null || filters.priceRange.max !== null)) {
            const { min, max } = filters.priceRange
            let priceLabel = '价格: '
            if (min !== null && max !== null) {
                priceLabel += `¥${min} - ¥${max}`
            } else if (min !== null) {
                priceLabel += `≥¥${min}`
            } else if (max !== null) {
                priceLabel += `≤¥${max}`
            }
            
            formatted.push({
                type: 'priceRange',
                label: '价格范围',
                values: [priceLabel],
                count: 1
            })
        }
        
        // 自定义筛选器
        if (filters.custom && config.customFilters) {
            config.customFilters.forEach(filter => {
                const value = filters.custom[filter.key]
                if (value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0)) {
                    let displayValues = []
                    
                    if (Array.isArray(value)) {
                        displayValues = value.map(v => {
                            const option = filter.options?.find(opt => opt.value === v)
                            return option?.label || v
                        })
                    } else {
                        const option = filter.options?.find(opt => opt.value === value)
                        displayValues = [option?.label || value]
                    }
                    
                    formatted.push({
                        type: 'custom',
                        key: filter.key,
                        label: filter.label,
                        values: displayValues,
                        count: displayValues.length
                    })
                }
            })
        }
        
        return formatted
    }
    
    // 计算筛选器激活数量
    const getActiveFilterCount = (filters) => {
        let count = 0
        
        if (filters.categories && filters.categories.length > 0) count += filters.categories.length
        if (filters.brands && filters.brands.length > 0) count += filters.brands.length
        if (filters.priceRange && (filters.priceRange.min !== null || filters.priceRange.max !== null)) count += 1
        
        if (filters.custom) {
            Object.values(filters.custom).forEach(value => {
                if (value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0)) {
                    count += Array.isArray(value) ? value.length : 1
                }
            })
        }
        
        return count
    }
    
    return {
        // 状态
        filterConfigs: readonly(filterConfigs),
        activeConfig: readonly(activeConfig),
        filterData: readonly(filterData),
        loading: readonly(loading),
        error: readonly(error),
        
        // Actions
        setFilterConfig,
        setActiveConfig,
        setFilterData,
        setLoading,
        setError,
        fetchFilterData,
        validateFilters,
        formatFiltersForDisplay,
        getActiveFilterCount
    }
})
