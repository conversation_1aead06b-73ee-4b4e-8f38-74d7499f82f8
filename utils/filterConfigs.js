// 筛选器配置文件
// 统一管理不同页面的筛选器配置

// 商品筛选器配置
export const productFilterConfig = {
    categories: [
        { label: 'UPS电源', value: 'ups' },
        { label: 'EPS电源', value: 'eps' },
        { label: '蓄电池', value: 'battery' },
        { label: '配电柜', value: 'distribution' },
        { label: '机房设备', value: 'server_room' },
        { label: '监控设备', value: 'monitoring' },
        { label: '防雷设备', value: 'lightning' },
        { label: '空调设备', value: 'hvac' },
        { label: '发电机组', value: 'generator' },
        { label: '变压器', value: 'transformer' }
    ],
    priceRange: true,
    priceRanges: [
        { label: '100元以下', min: 0, max: 100 },
        { label: '100-500元', min: 100, max: 500 },
        { label: '500-1000元', min: 500, max: 1000 },
        { label: '1000-5000元', min: 1000, max: 5000 },
        { label: '5000-10000元', min: 5000, max: 10000 },
        { label: '10000-50000元', min: 10000, max: 50000 },
        { label: '50000元以上', min: 50000, max: null }
    ],
    brands: [
        { label: '山特', value: 'santak' },
        { label: '科华', value: 'kelong' },
        { label: '易事特', value: 'east' },
        { label: '台达', value: 'delta' },
        { label: '艾默生', value: 'emerson' },
        { label: '施耐德', value: 'schneider' },
        { label: 'APC', value: 'apc' },
        { label: '华为', value: 'huawei' },
        { label: '中兴', value: 'zte' },
        { label: '英威腾', value: 'invt' },
        { label: 'ABB', value: 'abb' },
        { label: '西门子', value: 'siemens' }
    ],
    customFilters: [
        {
            key: 'power_rating',
            label: '功率等级',
            type: 'checkbox',
            options: [
                { label: '1kVA以下', value: 'under_1kva' },
                { label: '1-3kVA', value: '1_3kva' },
                { label: '3-6kVA', value: '3_6kva' },
                { label: '6-10kVA', value: '6_10kva' },
                { label: '10-20kVA', value: '10_20kva' },
                { label: '20-50kVA', value: '20_50kva' },
                { label: '50kVA以上', value: 'over_50kva' }
            ]
        },
        {
            key: 'voltage_level',
            label: '电压等级',
            type: 'radio',
            options: [
                { label: '220V', value: '220v' },
                { label: '380V', value: '380v' },
                { label: '双电压', value: 'dual' },
                { label: '其他', value: 'other' }
            ]
        },
        {
            key: 'certification',
            label: '认证标准',
            type: 'checkbox',
            options: [
                { label: 'CCC认证', value: 'ccc' },
                { label: 'CE认证', value: 'ce' },
                { label: 'ISO9001', value: 'iso9001' },
                { label: 'ISO14001', value: 'iso14001' },
                { label: 'ROHS', value: 'rohs' },
                { label: 'FCC', value: 'fcc' }
            ]
        },
        {
            key: 'warranty_period',
            label: '质保期限(年)',
            type: 'range',
            min: 1,
            max: 5,
            step: 1
        }
    ]
}

// 方案筛选器配置
export const solutionFilterConfig = {
    categories: [
        { label: '数据中心', value: 'datacenter' },
        { label: '安防监控', value: 'security' },
        { label: '交通运输', value: 'transport' },
        { label: '应急通信', value: 'emergency' },
        { label: '石油石化', value: 'petrochemical' },
        { label: '医疗设备', value: 'medical' },
        { label: '工业制造', value: 'manufacturing' },
        { label: '金融机构', value: 'finance' },
        { label: '教育机构', value: 'education' },
        { label: '政府机关', value: 'government' }
    ],
    priceRange: true,
    priceRanges: [
        { label: '1万以下', min: 0, max: 10000 },
        { label: '1-5万', min: 10000, max: 50000 },
        { label: '5-10万', min: 50000, max: 100000 },
        { label: '10-50万', min: 100000, max: 500000 },
        { label: '50-100万', min: 500000, max: 1000000 },
        { label: '100万以上', min: 1000000, max: null }
    ],
    brands: [
        { label: '华为', value: 'huawei' },
        { label: '施耐德', value: 'schneider' },
        { label: '艾默生', value: 'emerson' },
        { label: 'ABB', value: 'abb' },
        { label: '西门子', value: 'siemens' },
        { label: '台达', value: 'delta' },
        { label: '山特', value: 'santak' },
        { label: '科华', value: 'kelong' },
        { label: '易事特', value: 'east' },
        { label: '中兴', value: 'zte' }
    ],
    customFilters: [
        {
            key: 'application_scene',
            label: '应用场景',
            type: 'checkbox',
            options: [
                { label: '机房建设', value: 'server_room' },
                { label: '工厂车间', value: 'factory' },
                { label: '办公楼宇', value: 'office' },
                { label: '医院设施', value: 'hospital' },
                { label: '学校教育', value: 'education' },
                { label: '商业中心', value: 'commercial' },
                { label: '住宅小区', value: 'residential' },
                { label: '交通枢纽', value: 'transport_hub' }
            ]
        },
        {
            key: 'power_range',
            label: '功率范围',
            type: 'radio',
            options: [
                { label: '小功率(≤10kVA)', value: 'small' },
                { label: '中功率(10-100kVA)', value: 'medium' },
                { label: '大功率(100-500kVA)', value: 'large' },
                { label: '超大功率(≥500kVA)', value: 'extra_large' }
            ]
        },
        {
            key: 'solution_type',
            label: '方案类型',
            type: 'checkbox',
            options: [
                { label: '标准方案', value: 'standard' },
                { label: '定制方案', value: 'custom' },
                { label: '集成方案', value: 'integrated' },
                { label: '升级方案', value: 'upgrade' }
            ]
        },
        {
            key: 'solution_complexity',
            label: '方案复杂度',
            type: 'range',
            min: 1,
            max: 10,
            step: 1
        }
    ]
}

// 排序选项配置
export const sortOptions = {
    product: [
        { label: '综合排序', value: 'default' },
        { label: '销量优先', value: 'sales_desc' },
        { label: '价格从低到高', value: 'price_asc' },
        { label: '价格从高到低', value: 'price_desc' },
        { label: '最新上架', value: 'created_desc' },
        { label: '好评优先', value: 'rating_desc' }
    ],
    solution: [
        { label: '综合排序', value: 'default' },
        { label: '最新发布', value: 'created_desc' },
        { label: '最受欢迎', value: 'popular_desc' },
        { label: '价格从低到高', value: 'price_asc' },
        { label: '价格从高到低', value: 'price_desc' },
        { label: '完成度优先', value: 'completion_desc' }
    ]
}

// 获取筛选器配置的工具函数
export const getFilterConfig = (type) => {
    switch (type) {
        case 'product':
            return productFilterConfig
        case 'solution':
            return solutionFilterConfig
        default:
            return {}
    }
}

// 获取排序选项的工具函数
export const getSortOptions = (type) => {
    return sortOptions[type] || sortOptions.product
}

// 验证筛选器值的工具函数
export const validateFilterValue = (filterConfig, key, value) => {
    const filter = filterConfig.customFilters?.find(f => f.key === key)
    if (!filter) return value
    
    switch (filter.type) {
        case 'radio':
            const validRadioOptions = filter.options.map(opt => opt.value)
            return validRadioOptions.includes(value) ? value : ''
            
        case 'checkbox':
            if (!Array.isArray(value)) return []
            const validCheckboxOptions = filter.options.map(opt => opt.value)
            return value.filter(v => validCheckboxOptions.includes(v))
            
        case 'range':
            if (!Array.isArray(value) || value.length !== 2) {
                return [filter.min || 0, filter.max || 100]
            }
            return [
                Math.max(value[0], filter.min || 0),
                Math.min(value[1], filter.max || 100)
            ]
            
        default:
            return value
    }
}

// 格式化筛选器显示文本的工具函数
export const formatFilterDisplayText = (filterConfig, filters) => {
    const displayTexts = []
    
    // 分类
    if (filters.categories && filters.categories.length > 0) {
        const categoryLabels = filters.categories.map(cat => {
            const categoryConfig = filterConfig.categories?.find(c => c.value === cat)
            return categoryConfig?.label || cat
        })
        displayTexts.push(`分类: ${categoryLabels.join(', ')}`)
    }
    
    // 品牌
    if (filters.brands && filters.brands.length > 0) {
        const brandLabels = filters.brands.map(brand => {
            const brandConfig = filterConfig.brands?.find(b => b.value === brand)
            return brandConfig?.label || brand
        })
        displayTexts.push(`品牌: ${brandLabels.join(', ')}`)
    }
    
    // 价格范围
    if (filters.priceRange && (filters.priceRange.min !== null || filters.priceRange.max !== null)) {
        const { min, max } = filters.priceRange
        let priceText = '价格: '
        if (min !== null && max !== null) {
            priceText += `¥${min} - ¥${max}`
        } else if (min !== null) {
            priceText += `≥¥${min}`
        } else if (max !== null) {
            priceText += `≤¥${max}`
        }
        displayTexts.push(priceText)
    }
    
    return displayTexts
}
